# 模型视图发布CI关系处理修复

## 问题描述
- **时间**: 2025-06-13 15:21:55
- **问题**: 模型视图发布时不会为CI的关联资产数据创建关系，而普通视图发布会创建关系
- **影响**: 模型视图发布后，CI之间的关联关系缺失，影响数据完整性

## 问题分析

### 代码路径差异
1. **模型视图发布路径**（GeneralPushSvcImpl.publishDiagram 第158行）：
   ```java
   eamMergeSvc.modelPush(new ArrayList<>(), dEnergyId, releaseDesc, dirId);
   ```
   - 直接调用 `EamMergeSvcImpl.modelPush()` 方法
   - 通过 `EamMergePreProcessor.sync()` 进行数据同步
   - 最终通过 `EamCiRltMerge.push()` 处理CI和关系数据

2. **普通视图发布路径**（GeneralPushSvcImpl.publishDiagram 第170-172行）：
   ```java
   List<CcCiInfo> ccCiInfos = processCiRltSvc.dealPublishDiagramCI(changeCIDataByDEnergyId, ownerCode);
   List<ESCIRltInfo> esciRltInfos = processCiRltSvc.dealPublishDiagramRlt(privateAndDesginDataByDEnergyId, ccCiInfos);
   ```
   - 先调用 `dealPublishDiagramCI()` 处理CI数据
   - 再调用 `dealPublishDiagramRlt()` 处理关系数据

### 关键差异
模型视图发布缺少了 `dealPublishDiagramRlt()` 方法的调用，该方法负责创建CI的关联资产数据关系。

## 解决方案

### 选择方案2：修改EamCiRltMerge.push()方法
在 `EamCiRltMerge.push()` 方法中补充与 `dealPublishDiagramRlt()` 相同的关系处理逻辑。

### 修改内容

#### 1. 添加必要的导入
```java
import com.uinnova.product.eam.service.es.IamsESCIRltPirvateSvc;
import com.uino.bean.cmdb.business.BindCiRltRequestDto;
```

#### 2. 添加服务注入
```java
@Resource
private IamsESCIRltPirvateSvc esCIRltPirvateSvc;
```

#### 3. 修改push()方法
在现有关系处理逻辑之后，添加调用：
```java
// 补充处理CI关联资产数据关系，类似dealPublishDiagramRlt的逻辑
handleCiRelationships(params, saveList);
```

#### 4. 新增handleCiRelationships()方法
实现类似 `dealPublishDiagramRlt()` 的关系处理逻辑：
- 构建CI Code映射
- 处理私有库和设计库的关系数据
- 区分新建和更新关系
- 批量保存关系数据
- 更新版本信息

## 修改文件
- `eam-app\eam-service\src\main\java\com\uinnova\product\eam\service\merge\impl\EamCiRltMerge.java`

## 预期效果
修改后，模型视图发布将能够正确创建CI的关联资产数据关系，与普通视图发布保持一致的行为。

## 测试建议
1. 测试模型视图发布功能
2. 验证CI关联关系是否正确创建
3. 确保不影响现有的模型发布功能
4. 对比模型视图发布和普通视图发布的关系创建结果
