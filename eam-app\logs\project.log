2025-06-13 11:28:59.043 INFO [main] com.uinnova.product.eam.EamApplication : 开始加载eam-web模块
2025-06-13 11:28:59.594 INFO [restartedMain] com.uinnova.product.eam.EamApplication : 开始加载eam-web模块
2025-06-13 11:29:00.405 WARN [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
2025-06-13 11:29:00.406 WARN [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
2025-06-13 11:29:00.696 INFO [restartedMain] com.uinnova.product.eam.EamApplication : Starting EamApplication using Java ******** with PID 36336 (D:\workspace\mycode\ea\eam-app\eam-web\target\classes started by lichong in D:\workspace\mycode\ea\eam-app)
2025-06-13 11:29:00.705 INFO [restartedMain] com.uinnova.product.eam.EamApplication : The following 1 profile is active: "local"
2025-06-13 11:29:07.287 INFO [restartedMain] com.uino.api.init.LocalRunConfig : 发现spring-boot配置为本地加载
2025-06-13 11:29:07.358 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : 开始注册Filter相关信息
2025-06-13 11:29:09.345 WARN [restartedMain] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker : Bean 'redisConfig' of type [com.uinnova.product.eam.config.RedisConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-13 11:29:09.400 INFO [restartedMain] com.uinnova.product.eam.config.RedisConfig : 初始化 -> [Redis CacheErrorHandler]
2025-06-13 11:29:09.405 WARN [restartedMain] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker : Bean 'errorHandler' of type [com.uinnova.product.eam.config.RedisConfig$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-13 11:29:12.120 INFO [restartedMain] org.apache.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-1515"]
2025-06-13 11:29:12.123 INFO [restartedMain] org.apache.catalina.core.StandardService : Starting service [Tomcat]
2025-06-13 11:29:12.124 INFO [restartedMain] org.apache.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/11.0.6]
2025-06-13 11:29:12.435 INFO [restartedMain] org.apache.jasper.servlet.TldScanner : At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-06-13 11:29:12.445 INFO [restartedMain] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/tarsier-eam] : Initializing Spring embedded WebApplicationContext
2025-06-13 11:29:12.467 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : ResponseFilter注册成功
2025-06-13 11:29:12.469 INFO [restartedMain] com.uino.init.http.ResponseFilter : response请求拦截器注册成功
2025-06-13 11:29:12.527 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : RefererFilter注册成功
2025-06-13 11:29:12.533 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : StaticFilter注册成功
2025-06-13 11:29:12.535 INFO [restartedMain] com.uino.init.http.StaticFilter : 静态资源请求拦截器注册成功
2025-06-13 11:29:12.542 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : PermissionFilter注册成功
2025-06-13 11:29:12.550 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : PluginOperateLogFilter注册成功
2025-06-13 11:29:19.157 WARN [restartedMain] com.amazonaws.util.VersionInfoUtils : The AWS SDK for Java 1.x entered maintenance mode starting July 31, 2024 and will reach end of support on December 31, 2025. For more information, see https://aws.amazon.com/blogs/developer/the-aws-sdk-for-java-1-x-is-in-maintenance-mode-effective-july-31-2024/\nYou can print where on the file system the AWS SDK for Java 1.x core runtime is located by setting the AWS_JAVA_V1_PRINT_LOCATION environment variable or aws.java.v1.printLocation system property to 'true'.\nThis message can be disabled by setting the AWS_JAVA_V1_DISABLE_DEPRECATION_ANNOUNCEMENT environment variable or aws.java.v1.disableDeprecationAnnouncement system property to 'true'.\nThe AWS SDK for Java 1.x is being used here:\nat java.base/java.lang.Thread.getStackTrace(Thread.java:1610)\nat com.amazonaws.util.VersionInfoUtils.printDeprecationAnnouncement(VersionInfoUtils.java:81)\nat com.amazonaws.util.VersionInfoUtils.<clinit>(VersionInfoUtils.java:59)\nat com.amazonaws.ClientConfiguration.<clinit>(ClientConfiguration.java:95)\nat com.uino.util.rsm.AliyunObsRsmBehavior.initClient(AliyunObsRsmBehavior.java:48)\nat com.uino.util.rsm.AliyunObsRsmBehavior.<init>(AliyunObsRsmBehavior.java:35)\nat com.uino.util.rsm.RsmUtils.initRsmBehavior(RsmUtils.java:65)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\nat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\nat java.base/java.lang.reflect.Method.invoke(Method.java:568)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:429)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1810)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)\nat org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)\nat org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)\nat org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)\nat org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)\nat org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:318)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)\nat com.uinnova.product.eam.EamApplication.main(EamApplication.java:39)\nat com.uinnova.product.eam.web.test.RunLocal.main(RunLocal.java:10)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\nat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\nat java.base/java.lang.reflect.Method.invoke(Method.java:568)\nat org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
2025-06-13 11:29:21.638 INFO [restartedMain] com.uino.util.cache.config.RedissonConfiguration : RedissonClient, type : single
2025-06-13 11:29:21.651 INFO [restartedMain] com.uino.util.cache.config.RedissonConfiguration : Create redissonClient.
2025-06-13 11:29:26.732 INFO [restartedMain] com.uinnova.product.eam.init.EamLocalRunConfig$$SpringCGLIB$$0 : 发现spring-boot配置为本地加载
2025-06-13 11:29:34.953 INFO [restartedMain] com.uino.init.ControllerAspect : web请求日志切面注册成功
2025-06-13 11:29:34.988 INFO [restartedMain] com.uino.init.ExceptionController : 异常处理类注册成功
2025-06-13 11:29:34.999 INFO [restartedMain] com.uino.init.InitBeans : 开始注册i18-client，修改i18n.files可修改对应国际化文件读取，支持逗号隔开字符串
2025-06-13 11:29:35.017 INFO [restartedMain] com.uino.init.InitBeans : 注册i18-client成功
2025-06-13 11:29:35.019 INFO [restartedMain] com.uinnova.product.devcloud.i18n.client.support.AbstractI18nClient :  has refresh language data ... 
2025-06-13 11:29:35.019 INFO [restartedMain] com.uinnova.product.devcloud.i18n.client.support.AbstractI18nClient :  load refresh language data [114]. 
2025-06-13 11:29:35.085 INFO [restartedMain] com.uino.init.JudgeProcessBeans : 注册校验链
2025-06-13 11:29:35.445 INFO [restartedMain] com.uino.oauth.common.service.UinoLdapAuthenticationProvider : init  ldap user details manager !
2025-06-13 11:29:35.666 INFO [restartedMain] com.uino.monitor.tp.common.SpecifyIndexDao : init SpecifyIndexDao
2025-06-13 11:29:35.667 INFO [restartedMain] com.uino.monitor.tp.common.SpecifyIndexDao : 192.168.21.144:9200;
2025-06-13 11:29:37.187 INFO [restartedMain] com.uino.init.InitBeans : 国际化默认语言为【ZHC】，可通过修改i18n.lang.default修改默认语言，属性范围参考class:Language
2025-06-13 11:29:37.334 INFO [restartedMain] com.uino.init.InitFrameProp : FrameworkProperties注册成功, Local Space http://192.168.21.144/rsm
2025-06-13 11:29:37.707 WARN [restartedMain] org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer$AuthorizationManagerRequestMatcherRegistry : One of the patterns in [/system-setting#/license, ,/system/init/initFlowData, /eam/workbenchChargeDone/openApi/todoCount, /sys/getLogos, /eam/workbenchChargeDone/changeAction, /eam/workbenchChargeDone/saveOrUpdate, /sync/syncUserDataBatchToEa, /permission/oauth/resource/cleanOnlineUser, /wiki/getTokenByCode, /login/getLoginMethod, /cj/system/diagram/changeFlowByDiagramIds, /planDesign/updatePlanDiagramIsFlow, /redirectAuth, /getTokenByCode, /refreshToken, /cmdb/dataSet/execute, /eam/user/getUserByRoleName, /cmdb/dataSet/realTimeExecute, /websocket/*/*, /eam/oauth/getTokenByLoginInfo, /trial/saas/login/check, /eam/notice/workflow/msg/save, /planDesign/getPlanForFeign, /planDesign/findRenewVersionPlanList, /flowable/getApprovalUser, /flowable/approval/task, /flowable/batchModifyWorkbenchTask, /rsm/**, /ai/api/**, /webjars/css/*.css, /webjars/js/*.js, /swagger-resources, /v2/api-docs, /doc.html] is missing a leading slash. This is discouraged; please include the leading slash in all your request matcher patterns. In future versions of Spring Security, leaving out the leading slash will result in an exception.
2025-06-13 11:29:40.960 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Using default implementation for ThreadExecutor
2025-06-13 11:29:40.971 INFO [restartedMain] org.quartz.core.SchedulerSignalerImpl : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-13 11:29:40.971 INFO [restartedMain] org.quartz.core.QuartzScheduler : Quartz Scheduler v.2.3.2 created.
2025-06-13 11:29:40.972 INFO [restartedMain] org.quartz.simpl.RAMJobStore : RAMJobStore initialized.
2025-06-13 11:29:40.972 INFO [restartedMain] org.quartz.core.QuartzScheduler : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'\n  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.\n  NOT STARTED.\n  Currently in standby mode.\n  Number of jobs executed: 0\n  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.\n  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.\n
2025-06-13 11:29:40.972 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-13 11:29:40.973 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Quartz scheduler version: 2.3.2
2025-06-13 11:29:40.973 INFO [restartedMain] org.quartz.core.QuartzScheduler : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7ad0e4d0
2025-06-13 11:29:42.150 INFO [restartedMain] org.apache.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-1515"]
2025-06-13 11:29:43.315 INFO [restartedMain] com.alibaba.cloud.nacos.registry.NacosServiceRegistry : nacos registry, DEFAULT_GROUP eam-fuxi 192.168.205.1:1515 register finished
2025-06-13 11:29:43.443 INFO [restartedMain] org.quartz.core.QuartzScheduler : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-13 11:29:43.492 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 11:29:43.493 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 11:29:44.476 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 11:29:44.593 INFO [restartedMain] com.uinnova.product.eam.EamApplication : Started EamApplication in 44.989 seconds (process running for 47.815)
2025-06-13 11:29:44.754 INFO [restartedMain] com.uinnova.product.eam.service.utils.ConvertBusinessKeyUtil : 转换businessKey: false
2025-06-13 11:29:44.787 INFO [restartedMain] com.uino.service.util.FileUtil : FileUtil Initialization method called
2025-06-13 11:29:44.787 INFO [restartedMain] com.uinnova.product.eam.init.FlowSystemDataInit : 流程体系数据初始化开关关闭
2025-06-13 11:29:44.787 INFO [restartedMain] com.uino.service.util.FileUtil : FileUtil Initialization method called
2025-06-13 11:29:44.787 INFO [restartedMain] com.uinnova.product.eam.web.mix.diagram.v2.webSocekt.DiagramServer : DiagramServer start success
2025-06-13 11:29:44.790 INFO [restartedMain] com.uinnova.product.eam.EamApplication : EAM application start success :)
2025-06-13 11:30:56.919 INFO [http-nio-1515-exec-1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/tarsier-eam] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 11:30:57.195 INFO [http-nio-1515-exec-1] com.uino.init.ControllerAspect : 接收到来自【127.0.0.1】请求，uri为【/tarsier-eam/eam/ciRlt/getAutoDiagramRlt】,请求标识为【null】
2025-06-13 11:30:57.214 INFO [http-nio-1515-exec-1] com.uino.init.ControllerAspect : 参数【rltAutoDiagramVo】为【{"artifactId":3181127342708098,"checkOutLocal":true,"ciCode":"6096945768403801","down":1,"up":1}】
2025-06-13 11:31:22.810 INFO [http-nio-1515-exec-3] com.uino.init.ControllerAspect : 接收到来自【127.0.0.1】请求，uri为【/tarsier-eam/eam/ciRlt/getAutoDiagramRlt】,请求标识为【null】
2025-06-13 11:31:22.815 INFO [http-nio-1515-exec-3] com.uino.init.ControllerAspect : 参数【rltAutoDiagramVo】为【{"artifactId":3181127342708098,"checkOutLocal":true,"ciCode":"6096945768403801","down":1,"up":1}】
2025-06-13 11:31:57.923 INFO [http-nio-1515-exec-5] com.uino.init.ControllerAspect : 接收到来自【127.0.0.1】请求，uri为【/tarsier-eam/eam/ciRlt/getAutoDiagramRlt】,请求标识为【null】
2025-06-13 11:31:57.929 INFO [http-nio-1515-exec-5] com.uino.init.ControllerAspect : 参数【rltAutoDiagramVo】为【{"artifactId":3181127342708098,"checkOutLocal":true,"ciCode":"6096945768403801","down":1,"up":1}】
2025-06-13 11:35:12.960 INFO [http-nio-1515-exec-4] com.uino.init.ControllerAspect : 接收到来自【127.0.0.1】请求，uri为【/tarsier-eam/eam/ciRlt/getAutoDiagramRlt】,请求标识为【null】
2025-06-13 11:35:12.967 INFO [http-nio-1515-exec-4] com.uino.init.ControllerAspect : 参数【rltAutoDiagramVo】为【{"artifactId":3181127342708098,"checkOutLocal":true,"ciCode":"6096945768403801","down":1,"up":1}】
2025-06-13 11:35:53.044 WARN [Thread-14] com.alibaba.nacos.common.notify.NotifyCenter : [NotifyCenter] Start destroying Publisher
2025-06-13 11:35:53.044 WARN [Thread-5] com.alibaba.nacos.common.executor.ThreadPoolManager : [ThreadPoolManager] Start destroying ThreadPool
2025-06-13 11:35:53.044 WARN [Thread-16] com.alibaba.nacos.common.http.HttpClientBeanHolder : [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-13 11:35:53.044 WARN [Thread-14] com.alibaba.nacos.common.notify.NotifyCenter : [NotifyCenter] Destruction of the end
2025-06-13 11:35:53.044 WARN [Thread-5] com.alibaba.nacos.common.executor.ThreadPoolManager : [ThreadPoolManager] Destruction of the end
2025-06-13 11:35:53.046 WARN [Thread-16] com.alibaba.nacos.common.http.HttpClientBeanHolder : [HttpClientBeanHolder] Destruction of the end
2025-06-13 11:35:53.049 INFO [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-13 11:37:25.719 INFO [main] com.uinnova.product.eam.EamApplication : 开始加载eam-web模块
2025-06-13 11:37:26.145 INFO [restartedMain] com.uinnova.product.eam.EamApplication : 开始加载eam-web模块
2025-06-13 11:37:26.810 WARN [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
2025-06-13 11:37:26.811 WARN [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
2025-06-13 11:37:27.069 INFO [restartedMain] com.uinnova.product.eam.EamApplication : Starting EamApplication using Java ******** with PID 41564 (D:\workspace\mycode\ea\eam-app\eam-web\target\classes started by lichong in D:\workspace\mycode\ea\eam-app)
2025-06-13 11:37:27.075 INFO [restartedMain] com.uinnova.product.eam.EamApplication : The following 1 profile is active: "local"
2025-06-13 11:37:32.604 INFO [restartedMain] com.uino.api.init.LocalRunConfig : 发现spring-boot配置为本地加载
2025-06-13 11:37:32.664 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : 开始注册Filter相关信息
2025-06-13 11:37:34.372 WARN [restartedMain] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker : Bean 'redisConfig' of type [com.uinnova.product.eam.config.RedisConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-13 11:37:34.392 INFO [restartedMain] com.uinnova.product.eam.config.RedisConfig : 初始化 -> [Redis CacheErrorHandler]
2025-06-13 11:37:34.394 WARN [restartedMain] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker : Bean 'errorHandler' of type [com.uinnova.product.eam.config.RedisConfig$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-13 11:37:36.553 INFO [restartedMain] org.apache.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-1515"]
2025-06-13 11:37:36.558 INFO [restartedMain] org.apache.catalina.core.StandardService : Starting service [Tomcat]
2025-06-13 11:37:36.558 INFO [restartedMain] org.apache.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/11.0.6]
2025-06-13 11:37:36.862 INFO [restartedMain] org.apache.jasper.servlet.TldScanner : At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-06-13 11:37:36.871 INFO [restartedMain] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/tarsier-eam] : Initializing Spring embedded WebApplicationContext
2025-06-13 11:37:36.890 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : ResponseFilter注册成功
2025-06-13 11:37:36.893 INFO [restartedMain] com.uino.init.http.ResponseFilter : response请求拦截器注册成功
2025-06-13 11:37:36.930 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : RefererFilter注册成功
2025-06-13 11:37:36.935 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : StaticFilter注册成功
2025-06-13 11:37:36.937 INFO [restartedMain] com.uino.init.http.StaticFilter : 静态资源请求拦截器注册成功
2025-06-13 11:37:36.943 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : PermissionFilter注册成功
2025-06-13 11:37:36.950 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : PluginOperateLogFilter注册成功
2025-06-13 11:37:43.402 WARN [restartedMain] com.amazonaws.util.VersionInfoUtils : The AWS SDK for Java 1.x entered maintenance mode starting July 31, 2024 and will reach end of support on December 31, 2025. For more information, see https://aws.amazon.com/blogs/developer/the-aws-sdk-for-java-1-x-is-in-maintenance-mode-effective-july-31-2024/\nYou can print where on the file system the AWS SDK for Java 1.x core runtime is located by setting the AWS_JAVA_V1_PRINT_LOCATION environment variable or aws.java.v1.printLocation system property to 'true'.\nThis message can be disabled by setting the AWS_JAVA_V1_DISABLE_DEPRECATION_ANNOUNCEMENT environment variable or aws.java.v1.disableDeprecationAnnouncement system property to 'true'.\nThe AWS SDK for Java 1.x is being used here:\nat java.base/java.lang.Thread.getStackTrace(Thread.java:1610)\nat com.amazonaws.util.VersionInfoUtils.printDeprecationAnnouncement(VersionInfoUtils.java:81)\nat com.amazonaws.util.VersionInfoUtils.<clinit>(VersionInfoUtils.java:59)\nat com.amazonaws.ClientConfiguration.<clinit>(ClientConfiguration.java:95)\nat com.uino.util.rsm.AliyunObsRsmBehavior.initClient(AliyunObsRsmBehavior.java:48)\nat com.uino.util.rsm.AliyunObsRsmBehavior.<init>(AliyunObsRsmBehavior.java:35)\nat com.uino.util.rsm.RsmUtils.initRsmBehavior(RsmUtils.java:65)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\nat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\nat java.base/java.lang.reflect.Method.invoke(Method.java:568)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:429)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1810)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)\nat org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)\nat org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)\nat org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)\nat org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)\nat org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:318)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)\nat com.uinnova.product.eam.EamApplication.main(EamApplication.java:39)\nat com.uinnova.product.eam.web.test.RunLocal.main(RunLocal.java:10)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\nat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\nat java.base/java.lang.reflect.Method.invoke(Method.java:568)\nat org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
2025-06-13 11:37:45.729 INFO [restartedMain] com.uino.util.cache.config.RedissonConfiguration : RedissonClient, type : single
2025-06-13 11:37:45.744 INFO [restartedMain] com.uino.util.cache.config.RedissonConfiguration : Create redissonClient.
2025-06-13 11:37:51.066 INFO [restartedMain] com.uinnova.product.eam.init.EamLocalRunConfig$$SpringCGLIB$$0 : 发现spring-boot配置为本地加载
2025-06-13 11:37:59.375 INFO [restartedMain] com.uino.init.ControllerAspect : web请求日志切面注册成功
2025-06-13 11:37:59.410 INFO [restartedMain] com.uino.init.ExceptionController : 异常处理类注册成功
2025-06-13 11:37:59.423 INFO [restartedMain] com.uino.init.InitBeans : 开始注册i18-client，修改i18n.files可修改对应国际化文件读取，支持逗号隔开字符串
2025-06-13 11:37:59.438 INFO [restartedMain] com.uino.init.InitBeans : 注册i18-client成功
2025-06-13 11:37:59.438 INFO [restartedMain] com.uinnova.product.devcloud.i18n.client.support.AbstractI18nClient :  has refresh language data ... 
2025-06-13 11:37:59.439 INFO [restartedMain] com.uinnova.product.devcloud.i18n.client.support.AbstractI18nClient :  load refresh language data [114]. 
2025-06-13 11:37:59.500 INFO [restartedMain] com.uino.init.JudgeProcessBeans : 注册校验链
2025-06-13 11:38:00.009 INFO [restartedMain] com.uino.oauth.common.service.UinoLdapAuthenticationProvider : init  ldap user details manager !
2025-06-13 11:38:00.267 INFO [restartedMain] com.uino.monitor.tp.common.SpecifyIndexDao : init SpecifyIndexDao
2025-06-13 11:38:00.267 INFO [restartedMain] com.uino.monitor.tp.common.SpecifyIndexDao : 192.168.21.144:9200;
2025-06-13 11:38:01.928 INFO [restartedMain] com.uino.init.InitBeans : 国际化默认语言为【ZHC】，可通过修改i18n.lang.default修改默认语言，属性范围参考class:Language
2025-06-13 11:38:02.096 INFO [restartedMain] com.uino.init.InitFrameProp : FrameworkProperties注册成功, Local Space http://192.168.21.144/rsm
2025-06-13 11:38:02.559 WARN [restartedMain] org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer$AuthorizationManagerRequestMatcherRegistry : One of the patterns in [/system-setting#/license, ,/system/init/initFlowData, /eam/workbenchChargeDone/openApi/todoCount, /sys/getLogos, /eam/workbenchChargeDone/changeAction, /eam/workbenchChargeDone/saveOrUpdate, /sync/syncUserDataBatchToEa, /permission/oauth/resource/cleanOnlineUser, /wiki/getTokenByCode, /login/getLoginMethod, /cj/system/diagram/changeFlowByDiagramIds, /planDesign/updatePlanDiagramIsFlow, /redirectAuth, /getTokenByCode, /refreshToken, /cmdb/dataSet/execute, /eam/user/getUserByRoleName, /cmdb/dataSet/realTimeExecute, /websocket/*/*, /eam/oauth/getTokenByLoginInfo, /trial/saas/login/check, /eam/notice/workflow/msg/save, /planDesign/getPlanForFeign, /planDesign/findRenewVersionPlanList, /flowable/getApprovalUser, /flowable/approval/task, /flowable/batchModifyWorkbenchTask, /rsm/**, /ai/api/**, /webjars/css/*.css, /webjars/js/*.js, /swagger-resources, /v2/api-docs, /doc.html] is missing a leading slash. This is discouraged; please include the leading slash in all your request matcher patterns. In future versions of Spring Security, leaving out the leading slash will result in an exception.
2025-06-13 11:38:06.091 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Using default implementation for ThreadExecutor
2025-06-13 11:38:06.104 INFO [restartedMain] org.quartz.core.SchedulerSignalerImpl : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-13 11:38:06.105 INFO [restartedMain] org.quartz.core.QuartzScheduler : Quartz Scheduler v.2.3.2 created.
2025-06-13 11:38:06.106 INFO [restartedMain] org.quartz.simpl.RAMJobStore : RAMJobStore initialized.
2025-06-13 11:38:06.106 INFO [restartedMain] org.quartz.core.QuartzScheduler : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'\n  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.\n  NOT STARTED.\n  Currently in standby mode.\n  Number of jobs executed: 0\n  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.\n  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.\n
2025-06-13 11:38:06.106 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-13 11:38:06.107 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Quartz scheduler version: 2.3.2
2025-06-13 11:38:06.107 INFO [restartedMain] org.quartz.core.QuartzScheduler : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6b12d6f1
2025-06-13 11:38:07.592 INFO [restartedMain] org.apache.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-1515"]
2025-06-13 11:38:09.142 INFO [restartedMain] com.alibaba.cloud.nacos.registry.NacosServiceRegistry : nacos registry, DEFAULT_GROUP eam-fuxi 192.168.205.1:1515 register finished
2025-06-13 11:38:09.286 INFO [restartedMain] org.quartz.core.QuartzScheduler : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-13 11:38:09.358 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 11:38:09.360 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 11:38:10.633 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 11:38:10.765 INFO [restartedMain] com.uinnova.product.eam.EamApplication : Started EamApplication in 44.612 seconds (process running for 46.646)
2025-06-13 11:38:10.778 INFO [restartedMain] com.uinnova.product.eam.init.FlowSystemDataInit : 流程体系数据初始化开关关闭
2025-06-13 11:38:10.804 INFO [restartedMain] com.uino.service.util.FileUtil : FileUtil Initialization method called
2025-06-13 11:38:10.805 INFO [restartedMain] com.uino.service.util.FileUtil : FileUtil Initialization method called
2025-06-13 11:38:10.807 INFO [restartedMain] com.uinnova.product.eam.web.mix.diagram.v2.webSocekt.DiagramServer : DiagramServer start success
2025-06-13 11:38:10.809 INFO [restartedMain] com.uinnova.product.eam.service.utils.ConvertBusinessKeyUtil : 转换businessKey: false
2025-06-13 11:38:10.967 INFO [restartedMain] com.uinnova.product.eam.EamApplication : EAM application start success :)
2025-06-13 11:38:49.347 INFO [http-nio-1515-exec-2] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/tarsier-eam] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 11:38:51.254 ERROR[http-nio-1515-exec-2] com.uino.init.ExceptionController : 未检测到国际化,返回原文,请检查国际化文件:Comparison method violates its general contract!
2025-06-13 11:38:51.254 ERROR[http-nio-1515-exec-2] com.uino.init.ExceptionController : server异常
java.lang.IllegalArgumentException: Comparison method violates its general contract!
	at java.base/java.util.TimSort.mergeHi(TimSort.java:903) ~[?:?]
	at java.base/java.util.TimSort.mergeAt(TimSort.java:520) ~[?:?]
	at java.base/java.util.TimSort.mergeForceCollapse(TimSort.java:461) ~[?:?]
	at java.base/java.util.TimSort.sort(TimSort.java:254) ~[?:?]
	at java.base/java.util.Arrays.sort(Arrays.java:1307) ~[?:?]
	at java.base/java.util.ArrayList.sort(ArrayList.java:1721) ~[?:?]
	at com.uinnova.product.eam.service.cj.service.impl.DirDiagramPlanServiceImpl.queryMyAllRecentView(DirDiagramPlanServiceImpl.java:711) ~[classes/:?]
	at com.uinnova.product.eam.service.cj.service.impl.DirDiagramPlanServiceImpl.getAllRecentlyDiagramAndPlan(DirDiagramPlanServiceImpl.java:540) ~[classes/:?]
	at com.uinnova.product.eam.web.cj.DirDiagramPlanController.getAllRecentlyDiagramAndPlan(DirDiagramPlanController.java:64) ~[classes/:?]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:633) ~[jakarta.servlet-api-6.1.0.jar:6.1.0]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:723) ~[jakarta.servlet-api-6.1.0.jar:6.1.0]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:130) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-11.0.6.jar:11.0.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at com.uino.init.http.safe.WebSafeFilter.doFilter(WebSafeFilter.java:33) ~[uino-eam-micro-web-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at com.uino.init.http.safe.WebSafeFilter.doFilter(WebSafeFilter.java:33) ~[uino-eam-micro-web-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at com.uino.init.http.ResponseFilter.doFilter(ResponseFilter.java:54) ~[uino-eam-micro-web-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at com.uino.oauth2.UinoAuthenticationFilter.doFilterInternal(UinoAuthenticationFilter.java:53) ~[uino-oauth-client-parent-EAM-2.9.1-RELEASE.jar:EAM-2.9.1-RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238) [spring-security-config-6.4.5.jar:6.4.5]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278) [spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) [spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) [spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:142) [spring-session-core-3.4.3.jar:3.4.3]
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82) [spring-session-core-3.4.3.jar:3.4.3]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278) [spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) [spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) [spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:79) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:396) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:59) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at java.base/java.lang.Thread.run(Thread.java:833) [?:?]
2025-06-13 11:38:51.314 WARN [http-nio-1515-exec-2] org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver : Resolved [java.lang.IllegalArgumentException: Comparison method violates its general contract!]
2025-06-13 11:42:19.674 ERROR[http-nio-1515-exec-1] com.uino.init.ExceptionController : 未检测到国际化,返回原文,请检查国际化文件:Comparison method violates its general contract!
2025-06-13 11:42:19.675 ERROR[http-nio-1515-exec-1] com.uino.init.ExceptionController : server异常
java.lang.IllegalArgumentException: Comparison method violates its general contract!
	at java.base/java.util.TimSort.mergeHi(TimSort.java:903) ~[?:?]
	at java.base/java.util.TimSort.mergeAt(TimSort.java:520) ~[?:?]
	at java.base/java.util.TimSort.mergeForceCollapse(TimSort.java:461) ~[?:?]
	at java.base/java.util.TimSort.sort(TimSort.java:254) ~[?:?]
	at java.base/java.util.Arrays.sort(Arrays.java:1307) ~[?:?]
	at java.base/java.util.ArrayList.sort(ArrayList.java:1721) ~[?:?]
	at com.uinnova.product.eam.service.cj.service.impl.DirDiagramPlanServiceImpl.queryMyAllRecentView(DirDiagramPlanServiceImpl.java:711) ~[classes/:?]
	at com.uinnova.product.eam.service.cj.service.impl.DirDiagramPlanServiceImpl.getAllRecentlyDiagramAndPlan(DirDiagramPlanServiceImpl.java:540) ~[classes/:?]
	at com.uinnova.product.eam.web.cj.DirDiagramPlanController.getAllRecentlyDiagramAndPlan(DirDiagramPlanController.java:64) ~[classes/:?]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:633) ~[jakarta.servlet-api-6.1.0.jar:6.1.0]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:723) ~[jakarta.servlet-api-6.1.0.jar:6.1.0]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:130) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-11.0.6.jar:11.0.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at com.uino.init.http.safe.WebSafeFilter.doFilter(WebSafeFilter.java:33) ~[uino-eam-micro-web-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at com.uino.init.http.safe.WebSafeFilter.doFilter(WebSafeFilter.java:33) ~[uino-eam-micro-web-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at com.uino.init.http.ResponseFilter.doFilter(ResponseFilter.java:54) ~[uino-eam-micro-web-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at com.uino.oauth2.UinoAuthenticationFilter.doFilterInternal(UinoAuthenticationFilter.java:53) ~[uino-oauth-client-parent-EAM-2.9.1-RELEASE.jar:EAM-2.9.1-RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238) [spring-security-config-6.4.5.jar:6.4.5]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278) [spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) [spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) [spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:142) [spring-session-core-3.4.3.jar:3.4.3]
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82) [spring-session-core-3.4.3.jar:3.4.3]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278) [spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) [spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) [spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:79) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:396) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:59) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at java.base/java.lang.Thread.run(Thread.java:833) [?:?]
2025-06-13 11:42:19.680 WARN [http-nio-1515-exec-1] org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver : Resolved [java.lang.IllegalArgumentException: Comparison method violates its general contract!]
2025-06-13 11:42:19.698 WARN [Thread-16] com.alibaba.nacos.common.http.HttpClientBeanHolder : [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-13 11:42:19.698 WARN [Thread-14] com.alibaba.nacos.common.notify.NotifyCenter : [NotifyCenter] Start destroying Publisher
2025-06-13 11:42:19.698 WARN [Thread-14] com.alibaba.nacos.common.notify.NotifyCenter : [NotifyCenter] Destruction of the end
2025-06-13 11:42:19.698 WARN [Thread-5] com.alibaba.nacos.common.executor.ThreadPoolManager : [ThreadPoolManager] Start destroying ThreadPool
2025-06-13 11:42:19.699 WARN [Thread-5] com.alibaba.nacos.common.executor.ThreadPoolManager : [ThreadPoolManager] Destruction of the end
2025-06-13 11:42:19.699 WARN [Thread-16] com.alibaba.nacos.common.http.HttpClientBeanHolder : [HttpClientBeanHolder] Destruction of the end
2025-06-13 11:42:19.704 INFO [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-13 11:42:25.185 INFO [main] com.uinnova.product.eam.EamApplication : 开始加载eam-web模块
2025-06-13 11:42:25.735 INFO [restartedMain] com.uinnova.product.eam.EamApplication : 开始加载eam-web模块
2025-06-13 11:42:26.438 WARN [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
2025-06-13 11:42:26.439 WARN [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
2025-06-13 11:42:26.677 INFO [restartedMain] com.uinnova.product.eam.EamApplication : Starting EamApplication using Java ******** with PID 47556 (D:\workspace\mycode\ea\eam-app\eam-web\target\classes started by lichong in D:\workspace\mycode\ea\eam-app)
2025-06-13 11:42:26.682 INFO [restartedMain] com.uinnova.product.eam.EamApplication : The following 1 profile is active: "local"
2025-06-13 11:42:32.097 INFO [restartedMain] com.uino.api.init.LocalRunConfig : 发现spring-boot配置为本地加载
2025-06-13 11:42:32.162 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : 开始注册Filter相关信息
2025-06-13 11:42:33.721 WARN [restartedMain] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker : Bean 'redisConfig' of type [com.uinnova.product.eam.config.RedisConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-13 11:42:33.742 INFO [restartedMain] com.uinnova.product.eam.config.RedisConfig : 初始化 -> [Redis CacheErrorHandler]
2025-06-13 11:42:33.745 WARN [restartedMain] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker : Bean 'errorHandler' of type [com.uinnova.product.eam.config.RedisConfig$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-13 11:42:35.841 INFO [restartedMain] org.apache.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-1515"]
2025-06-13 11:42:35.844 INFO [restartedMain] org.apache.catalina.core.StandardService : Starting service [Tomcat]
2025-06-13 11:42:35.844 INFO [restartedMain] org.apache.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/11.0.6]
2025-06-13 11:42:36.073 INFO [restartedMain] org.apache.jasper.servlet.TldScanner : At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-06-13 11:42:36.087 INFO [restartedMain] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/tarsier-eam] : Initializing Spring embedded WebApplicationContext
2025-06-13 11:42:36.105 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : ResponseFilter注册成功
2025-06-13 11:42:36.106 INFO [restartedMain] com.uino.init.http.ResponseFilter : response请求拦截器注册成功
2025-06-13 11:42:36.148 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : RefererFilter注册成功
2025-06-13 11:42:36.156 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : StaticFilter注册成功
2025-06-13 11:42:36.158 INFO [restartedMain] com.uino.init.http.StaticFilter : 静态资源请求拦截器注册成功
2025-06-13 11:42:36.164 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : PermissionFilter注册成功
2025-06-13 11:42:36.172 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : PluginOperateLogFilter注册成功
2025-06-13 11:42:42.292 WARN [restartedMain] com.amazonaws.util.VersionInfoUtils : The AWS SDK for Java 1.x entered maintenance mode starting July 31, 2024 and will reach end of support on December 31, 2025. For more information, see https://aws.amazon.com/blogs/developer/the-aws-sdk-for-java-1-x-is-in-maintenance-mode-effective-july-31-2024/\nYou can print where on the file system the AWS SDK for Java 1.x core runtime is located by setting the AWS_JAVA_V1_PRINT_LOCATION environment variable or aws.java.v1.printLocation system property to 'true'.\nThis message can be disabled by setting the AWS_JAVA_V1_DISABLE_DEPRECATION_ANNOUNCEMENT environment variable or aws.java.v1.disableDeprecationAnnouncement system property to 'true'.\nThe AWS SDK for Java 1.x is being used here:\nat java.base/java.lang.Thread.getStackTrace(Thread.java:1610)\nat com.amazonaws.util.VersionInfoUtils.printDeprecationAnnouncement(VersionInfoUtils.java:81)\nat com.amazonaws.util.VersionInfoUtils.<clinit>(VersionInfoUtils.java:59)\nat com.amazonaws.ClientConfiguration.<clinit>(ClientConfiguration.java:95)\nat com.uino.util.rsm.AliyunObsRsmBehavior.initClient(AliyunObsRsmBehavior.java:48)\nat com.uino.util.rsm.AliyunObsRsmBehavior.<init>(AliyunObsRsmBehavior.java:35)\nat com.uino.util.rsm.RsmUtils.initRsmBehavior(RsmUtils.java:65)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\nat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\nat java.base/java.lang.reflect.Method.invoke(Method.java:568)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:429)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1810)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)\nat org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)\nat org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)\nat org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)\nat org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)\nat org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:318)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)\nat com.uinnova.product.eam.EamApplication.main(EamApplication.java:39)\nat com.uinnova.product.eam.web.test.RunLocal.main(RunLocal.java:10)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\nat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\nat java.base/java.lang.reflect.Method.invoke(Method.java:568)\nat org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
2025-06-13 11:42:44.945 INFO [restartedMain] com.uino.util.cache.config.RedissonConfiguration : RedissonClient, type : single
2025-06-13 11:42:44.959 INFO [restartedMain] com.uino.util.cache.config.RedissonConfiguration : Create redissonClient.
2025-06-13 11:42:49.838 INFO [restartedMain] com.uinnova.product.eam.init.EamLocalRunConfig$$SpringCGLIB$$0 : 发现spring-boot配置为本地加载
2025-06-13 11:42:57.993 INFO [restartedMain] com.uino.init.ControllerAspect : web请求日志切面注册成功
2025-06-13 11:42:58.028 INFO [restartedMain] com.uino.init.ExceptionController : 异常处理类注册成功
2025-06-13 11:42:58.040 INFO [restartedMain] com.uino.init.InitBeans : 开始注册i18-client，修改i18n.files可修改对应国际化文件读取，支持逗号隔开字符串
2025-06-13 11:42:58.055 INFO [restartedMain] com.uino.init.InitBeans : 注册i18-client成功
2025-06-13 11:42:58.055 INFO [restartedMain] com.uinnova.product.devcloud.i18n.client.support.AbstractI18nClient :  has refresh language data ... 
2025-06-13 11:42:58.055 INFO [restartedMain] com.uinnova.product.devcloud.i18n.client.support.AbstractI18nClient :  load refresh language data [114]. 
2025-06-13 11:42:58.121 INFO [restartedMain] com.uino.init.JudgeProcessBeans : 注册校验链
2025-06-13 11:42:58.550 INFO [restartedMain] com.uino.oauth.common.service.UinoLdapAuthenticationProvider : init  ldap user details manager !
2025-06-13 11:42:58.838 INFO [restartedMain] com.uino.monitor.tp.common.SpecifyIndexDao : init SpecifyIndexDao
2025-06-13 11:42:58.841 INFO [restartedMain] com.uino.monitor.tp.common.SpecifyIndexDao : 192.168.21.144:9200;
2025-06-13 11:43:00.403 INFO [restartedMain] com.uino.init.InitBeans : 国际化默认语言为【ZHC】，可通过修改i18n.lang.default修改默认语言，属性范围参考class:Language
2025-06-13 11:43:00.555 INFO [restartedMain] com.uino.init.InitFrameProp : FrameworkProperties注册成功, Local Space http://192.168.21.144/rsm
2025-06-13 11:43:01.023 WARN [restartedMain] org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer$AuthorizationManagerRequestMatcherRegistry : One of the patterns in [/system-setting#/license, ,/system/init/initFlowData, /eam/workbenchChargeDone/openApi/todoCount, /sys/getLogos, /eam/workbenchChargeDone/changeAction, /eam/workbenchChargeDone/saveOrUpdate, /sync/syncUserDataBatchToEa, /permission/oauth/resource/cleanOnlineUser, /wiki/getTokenByCode, /login/getLoginMethod, /cj/system/diagram/changeFlowByDiagramIds, /planDesign/updatePlanDiagramIsFlow, /redirectAuth, /getTokenByCode, /refreshToken, /cmdb/dataSet/execute, /eam/user/getUserByRoleName, /cmdb/dataSet/realTimeExecute, /websocket/*/*, /eam/oauth/getTokenByLoginInfo, /trial/saas/login/check, /eam/notice/workflow/msg/save, /planDesign/getPlanForFeign, /planDesign/findRenewVersionPlanList, /flowable/getApprovalUser, /flowable/approval/task, /flowable/batchModifyWorkbenchTask, /rsm/**, /ai/api/**, /webjars/css/*.css, /webjars/js/*.js, /swagger-resources, /v2/api-docs, /doc.html] is missing a leading slash. This is discouraged; please include the leading slash in all your request matcher patterns. In future versions of Spring Security, leaving out the leading slash will result in an exception.
2025-06-13 11:43:04.265 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Using default implementation for ThreadExecutor
2025-06-13 11:43:04.285 INFO [restartedMain] org.quartz.core.SchedulerSignalerImpl : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-13 11:43:04.285 INFO [restartedMain] org.quartz.core.QuartzScheduler : Quartz Scheduler v.2.3.2 created.
2025-06-13 11:43:04.286 INFO [restartedMain] org.quartz.simpl.RAMJobStore : RAMJobStore initialized.
2025-06-13 11:43:04.287 INFO [restartedMain] org.quartz.core.QuartzScheduler : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'\n  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.\n  NOT STARTED.\n  Currently in standby mode.\n  Number of jobs executed: 0\n  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.\n  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.\n
2025-06-13 11:43:04.287 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-13 11:43:04.287 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Quartz scheduler version: 2.3.2
2025-06-13 11:43:04.287 INFO [restartedMain] org.quartz.core.QuartzScheduler : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4e34fc0b
2025-06-13 11:43:06.081 INFO [restartedMain] org.apache.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-1515"]
2025-06-13 11:43:07.316 INFO [restartedMain] com.alibaba.cloud.nacos.registry.NacosServiceRegistry : nacos registry, DEFAULT_GROUP eam-fuxi 192.168.205.1:1515 register finished
2025-06-13 11:43:07.439 INFO [restartedMain] org.quartz.core.QuartzScheduler : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-13 11:43:07.492 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 11:43:07.494 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 11:43:08.572 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 11:43:08.684 INFO [restartedMain] com.uinnova.product.eam.EamApplication : Started EamApplication in 42.942 seconds (process running for 45.797)
2025-06-13 11:43:08.831 INFO [restartedMain] com.uinnova.product.eam.init.FlowSystemDataInit : 流程体系数据初始化开关关闭
2025-06-13 11:43:08.846 INFO [restartedMain] com.uino.service.util.FileUtil : FileUtil Initialization method called
2025-06-13 11:43:08.846 INFO [restartedMain] com.uinnova.product.eam.web.mix.diagram.v2.webSocekt.DiagramServer : DiagramServer start success
2025-06-13 11:43:08.863 INFO [restartedMain] com.uinnova.product.eam.service.utils.ConvertBusinessKeyUtil : 转换businessKey: false
2025-06-13 11:43:08.864 INFO [restartedMain] com.uino.service.util.FileUtil : FileUtil Initialization method called
2025-06-13 11:43:08.866 INFO [restartedMain] com.uinnova.product.eam.EamApplication : EAM application start success :)
2025-06-13 11:43:32.244 INFO [http-nio-1515-exec-1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/tarsier-eam] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 11:55:55.637 WARN [Thread-16] com.alibaba.nacos.common.http.HttpClientBeanHolder : [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-13 11:55:55.637 WARN [Thread-5] com.alibaba.nacos.common.executor.ThreadPoolManager : [ThreadPoolManager] Start destroying ThreadPool
2025-06-13 11:55:55.637 WARN [Thread-14] com.alibaba.nacos.common.notify.NotifyCenter : [NotifyCenter] Start destroying Publisher
2025-06-13 11:55:55.637 WARN [Thread-14] com.alibaba.nacos.common.notify.NotifyCenter : [NotifyCenter] Destruction of the end
2025-06-13 11:55:55.637 WARN [Thread-5] com.alibaba.nacos.common.executor.ThreadPoolManager : [ThreadPoolManager] Destruction of the end
2025-06-13 11:55:55.637 WARN [Thread-16] com.alibaba.nacos.common.http.HttpClientBeanHolder : [HttpClientBeanHolder] Destruction of the end
2025-06-13 11:55:55.643 INFO [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-13 14:03:36.392 INFO [main] com.uinnova.product.eam.EamApplication : 开始加载eam-web模块
2025-06-13 14:03:36.862 INFO [restartedMain] com.uinnova.product.eam.EamApplication : 开始加载eam-web模块
2025-06-13 14:03:37.547 WARN [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
2025-06-13 14:03:37.549 WARN [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
2025-06-13 14:03:37.836 INFO [restartedMain] com.uinnova.product.eam.EamApplication : Starting EamApplication using Java ******** with PID 48528 (D:\workspace\mycode\ea\eam-app\eam-web\target\classes started by lichong in D:\workspace\mycode\ea\eam-app)
2025-06-13 14:03:37.842 INFO [restartedMain] com.uinnova.product.eam.EamApplication : The following 1 profile is active: "local"
2025-06-13 14:03:43.363 INFO [restartedMain] com.uino.api.init.LocalRunConfig : 发现spring-boot配置为本地加载
2025-06-13 14:03:43.411 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : 开始注册Filter相关信息
2025-06-13 14:03:44.863 WARN [restartedMain] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker : Bean 'redisConfig' of type [com.uinnova.product.eam.config.RedisConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-13 14:03:44.877 INFO [restartedMain] com.uinnova.product.eam.config.RedisConfig : 初始化 -> [Redis CacheErrorHandler]
2025-06-13 14:03:44.879 WARN [restartedMain] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker : Bean 'errorHandler' of type [com.uinnova.product.eam.config.RedisConfig$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-13 14:03:46.711 INFO [restartedMain] org.apache.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-1515"]
2025-06-13 14:03:46.713 INFO [restartedMain] org.apache.catalina.core.StandardService : Starting service [Tomcat]
2025-06-13 14:03:46.715 INFO [restartedMain] org.apache.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/11.0.6]
2025-06-13 14:03:46.931 INFO [restartedMain] org.apache.jasper.servlet.TldScanner : At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-06-13 14:03:46.941 INFO [restartedMain] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/tarsier-eam] : Initializing Spring embedded WebApplicationContext
2025-06-13 14:03:46.957 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : ResponseFilter注册成功
2025-06-13 14:03:46.958 INFO [restartedMain] com.uino.init.http.ResponseFilter : response请求拦截器注册成功
2025-06-13 14:03:46.994 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : RefererFilter注册成功
2025-06-13 14:03:47.000 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : StaticFilter注册成功
2025-06-13 14:03:47.002 INFO [restartedMain] com.uino.init.http.StaticFilter : 静态资源请求拦截器注册成功
2025-06-13 14:03:47.007 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : PermissionFilter注册成功
2025-06-13 14:03:47.015 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : PluginOperateLogFilter注册成功
2025-06-13 14:03:53.245 WARN [restartedMain] com.amazonaws.util.VersionInfoUtils : The AWS SDK for Java 1.x entered maintenance mode starting July 31, 2024 and will reach end of support on December 31, 2025. For more information, see https://aws.amazon.com/blogs/developer/the-aws-sdk-for-java-1-x-is-in-maintenance-mode-effective-july-31-2024/\nYou can print where on the file system the AWS SDK for Java 1.x core runtime is located by setting the AWS_JAVA_V1_PRINT_LOCATION environment variable or aws.java.v1.printLocation system property to 'true'.\nThis message can be disabled by setting the AWS_JAVA_V1_DISABLE_DEPRECATION_ANNOUNCEMENT environment variable or aws.java.v1.disableDeprecationAnnouncement system property to 'true'.\nThe AWS SDK for Java 1.x is being used here:\nat java.base/java.lang.Thread.getStackTrace(Thread.java:1610)\nat com.amazonaws.util.VersionInfoUtils.printDeprecationAnnouncement(VersionInfoUtils.java:81)\nat com.amazonaws.util.VersionInfoUtils.<clinit>(VersionInfoUtils.java:59)\nat com.amazonaws.ClientConfiguration.<clinit>(ClientConfiguration.java:95)\nat com.uino.util.rsm.AliyunObsRsmBehavior.initClient(AliyunObsRsmBehavior.java:48)\nat com.uino.util.rsm.AliyunObsRsmBehavior.<init>(AliyunObsRsmBehavior.java:35)\nat com.uino.util.rsm.RsmUtils.initRsmBehavior(RsmUtils.java:65)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\nat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\nat java.base/java.lang.reflect.Method.invoke(Method.java:568)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:429)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1810)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)\nat org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)\nat org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)\nat org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)\nat org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)\nat org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:318)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)\nat com.uinnova.product.eam.EamApplication.main(EamApplication.java:39)\nat com.uinnova.product.eam.web.test.RunLocal.main(RunLocal.java:10)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\nat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\nat java.base/java.lang.reflect.Method.invoke(Method.java:568)\nat org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
2025-06-13 14:03:55.430 INFO [restartedMain] com.uino.util.cache.config.RedissonConfiguration : RedissonClient, type : single
2025-06-13 14:03:55.441 INFO [restartedMain] com.uino.util.cache.config.RedissonConfiguration : Create redissonClient.
2025-06-13 14:04:00.127 INFO [restartedMain] com.uinnova.product.eam.init.EamLocalRunConfig$$SpringCGLIB$$0 : 发现spring-boot配置为本地加载
2025-06-13 14:04:07.846 INFO [restartedMain] com.uino.init.ControllerAspect : web请求日志切面注册成功
2025-06-13 14:04:07.883 INFO [restartedMain] com.uino.init.ExceptionController : 异常处理类注册成功
2025-06-13 14:04:07.896 INFO [restartedMain] com.uino.init.InitBeans : 开始注册i18-client，修改i18n.files可修改对应国际化文件读取，支持逗号隔开字符串
2025-06-13 14:04:07.911 INFO [restartedMain] com.uino.init.InitBeans : 注册i18-client成功
2025-06-13 14:04:07.912 INFO [restartedMain] com.uinnova.product.devcloud.i18n.client.support.AbstractI18nClient :  has refresh language data ... 
2025-06-13 14:04:07.912 INFO [restartedMain] com.uinnova.product.devcloud.i18n.client.support.AbstractI18nClient :  load refresh language data [114]. 
2025-06-13 14:04:07.988 INFO [restartedMain] com.uino.init.JudgeProcessBeans : 注册校验链
2025-06-13 14:04:08.406 INFO [restartedMain] com.uino.oauth.common.service.UinoLdapAuthenticationProvider : init  ldap user details manager !
2025-06-13 14:04:08.615 INFO [restartedMain] com.uino.monitor.tp.common.SpecifyIndexDao : init SpecifyIndexDao
2025-06-13 14:04:08.616 INFO [restartedMain] com.uino.monitor.tp.common.SpecifyIndexDao : 192.168.21.144:9200;
2025-06-13 14:04:10.061 INFO [restartedMain] com.uino.init.InitBeans : 国际化默认语言为【ZHC】，可通过修改i18n.lang.default修改默认语言，属性范围参考class:Language
2025-06-13 14:04:10.187 INFO [restartedMain] com.uino.init.InitFrameProp : FrameworkProperties注册成功, Local Space http://192.168.21.144/rsm
2025-06-13 14:04:10.587 WARN [restartedMain] org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer$AuthorizationManagerRequestMatcherRegistry : One of the patterns in [/system-setting#/license, ,/system/init/initFlowData, /eam/workbenchChargeDone/openApi/todoCount, /sys/getLogos, /eam/workbenchChargeDone/changeAction, /eam/workbenchChargeDone/saveOrUpdate, /sync/syncUserDataBatchToEa, /permission/oauth/resource/cleanOnlineUser, /wiki/getTokenByCode, /login/getLoginMethod, /cj/system/diagram/changeFlowByDiagramIds, /planDesign/updatePlanDiagramIsFlow, /redirectAuth, /getTokenByCode, /refreshToken, /cmdb/dataSet/execute, /eam/user/getUserByRoleName, /cmdb/dataSet/realTimeExecute, /websocket/*/*, /eam/oauth/getTokenByLoginInfo, /trial/saas/login/check, /eam/notice/workflow/msg/save, /planDesign/getPlanForFeign, /planDesign/findRenewVersionPlanList, /flowable/getApprovalUser, /flowable/approval/task, /flowable/batchModifyWorkbenchTask, /rsm/**, /ai/api/**, /webjars/css/*.css, /webjars/js/*.js, /swagger-resources, /v2/api-docs, /doc.html] is missing a leading slash. This is discouraged; please include the leading slash in all your request matcher patterns. In future versions of Spring Security, leaving out the leading slash will result in an exception.
2025-06-13 14:04:13.734 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Using default implementation for ThreadExecutor
2025-06-13 14:04:13.744 INFO [restartedMain] org.quartz.core.SchedulerSignalerImpl : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-13 14:04:13.745 INFO [restartedMain] org.quartz.core.QuartzScheduler : Quartz Scheduler v.2.3.2 created.
2025-06-13 14:04:13.745 INFO [restartedMain] org.quartz.simpl.RAMJobStore : RAMJobStore initialized.
2025-06-13 14:04:13.746 INFO [restartedMain] org.quartz.core.QuartzScheduler : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'\n  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.\n  NOT STARTED.\n  Currently in standby mode.\n  Number of jobs executed: 0\n  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.\n  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.\n
2025-06-13 14:04:13.746 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-13 14:04:13.746 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Quartz scheduler version: 2.3.2
2025-06-13 14:04:13.746 INFO [restartedMain] org.quartz.core.QuartzScheduler : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6fdc5240
2025-06-13 14:04:15.081 INFO [restartedMain] org.apache.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-1515"]
2025-06-13 14:04:16.326 INFO [restartedMain] com.alibaba.cloud.nacos.registry.NacosServiceRegistry : nacos registry, DEFAULT_GROUP eam-fuxi 192.168.205.1:1515 register finished
2025-06-13 14:04:16.454 INFO [restartedMain] org.quartz.core.QuartzScheduler : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-13 14:04:16.513 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 14:04:16.515 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 14:04:17.538 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 14:04:17.659 INFO [restartedMain] com.uinnova.product.eam.EamApplication : Started EamApplication in 40.789 seconds (process running for 42.989)
2025-06-13 14:04:17.693 INFO [restartedMain] com.uino.service.util.FileUtil : FileUtil Initialization method called
2025-06-13 14:04:17.693 INFO [restartedMain] com.uinnova.product.eam.service.utils.ConvertBusinessKeyUtil : 转换businessKey: false
2025-06-13 14:04:17.712 INFO [restartedMain] com.uinnova.product.eam.web.mix.diagram.v2.webSocekt.DiagramServer : DiagramServer start success
2025-06-13 14:04:17.713 INFO [restartedMain] com.uino.service.util.FileUtil : FileUtil Initialization method called
2025-06-13 14:04:17.713 INFO [restartedMain] com.uinnova.product.eam.init.FlowSystemDataInit : 流程体系数据初始化开关关闭
2025-06-13 14:04:17.832 INFO [restartedMain] com.uinnova.product.eam.EamApplication : EAM application start success :)
2025-06-13 14:10:10.080 INFO [http-nio-1515-exec-8] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/tarsier-eam] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:10:10.356 INFO [http-nio-1515-exec-8] com.uino.init.ControllerAspect : 接收到来自【127.0.0.1】请求，uri为【/tarsier-eam/eam/ciRlt/getAutoDiagramRlt】,请求标识为【null】
2025-06-13 14:10:10.374 INFO [http-nio-1515-exec-8] com.uino.init.ControllerAspect : 参数【rltAutoDiagramVo】为【{"artifactId":3181127342708098,"checkOutLocal":true,"ciCode":"1797209602665552","down":1,"up":1}】
2025-06-13 14:12:15.346 INFO [http-nio-1515-exec-3] com.uino.init.ControllerAspect : 接收到来自【127.0.0.1】请求，uri为【/tarsier-eam/eam/ciRlt/getAutoDiagramRlt】,请求标识为【null】
2025-06-13 14:12:15.354 INFO [http-nio-1515-exec-3] com.uino.init.ControllerAspect : 参数【rltAutoDiagramVo】为【{"artifactId":3181127342708098,"checkOutLocal":true,"ciCode":"1797209602665552","down":1,"up":1}】
2025-06-13 14:18:06.582 INFO [http-nio-1515-exec-7] com.uino.init.ControllerAspect : 接收到来自【127.0.0.1】请求，uri为【/tarsier-eam/eam/ciRlt/getAutoDiagramRlt】,请求标识为【null】
2025-06-13 14:18:06.588 INFO [http-nio-1515-exec-7] com.uino.init.ControllerAspect : 参数【rltAutoDiagramVo】为【{"artifactId":3181127342708098,"checkOutLocal":true,"ciCode":"1797209602665552","down":1,"up":1}】
2025-06-13 14:18:10.479 ERROR[http-nio-1515-exec-3] com.uino.init.ExceptionController : 未检测到国际化,返回原文,请检查国际化文件:ServletOutputStream failed to flush: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
2025-06-13 14:18:10.479 ERROR[http-nio-1515-exec-3] com.uino.init.ExceptionController : server异常
org.springframework.web.context.request.async.AsyncRequestNotUsableException: ServletOutputStream failed to flush: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleHttpServletResponse.handleIOException(StandardServletAsyncWebRequest.java:346) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.flush(StandardServletAsyncWebRequest.java:418) ~[spring-web-6.2.6.jar:6.2.6]
	at java.base/java.io.FilterOutputStream.flush(FilterOutputStream.java:153) ~[?:?]
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1205) ~[jackson-core-2.18.3.jar:2.18.3]
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1063) ~[jackson-databind-2.18.3.jar:2.18.3]
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:486) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:126) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:345) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:208) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:136) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:653) ~[jakarta.servlet-api-6.1.0.jar:6.1.0]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:723) ~[jakarta.servlet-api-6.1.0.jar:6.1.0]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:130) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-11.0.6.jar:11.0.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at com.uino.init.http.safe.WebSafeFilter.doFilter(WebSafeFilter.java:33) ~[uino-eam-micro-web-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at com.uino.init.http.safe.WebSafeFilter.doFilter(WebSafeFilter.java:33) ~[uino-eam-micro-web-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at com.uino.init.http.ResponseFilter.doFilter(ResponseFilter.java:54) ~[uino-eam-micro-web-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at com.uino.oauth2.UinoAuthenticationFilter.doFilterInternal(UinoAuthenticationFilter.java:53) ~[uino-oauth-client-parent-EAM-2.9.1-RELEASE.jar:EAM-2.9.1-RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191) ~[spring-security-web-6.4.5.jar:6.4.5]
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113) ~[spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243) ~[spring-webmvc-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:238) [spring-security-config-6.4.5.jar:6.4.5]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278) [spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) [spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) [spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:142) [spring-session-core-3.4.3.jar:3.4.3]
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82) [spring-session-core-3.4.3.jar:3.4.3]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278) [spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) [spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-6.2.6.jar:6.2.6]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116) [spring-web-6.2.6.jar:6.2.6]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:109) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:79) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:396) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:59) [tomcat-embed-core-11.0.6.jar:11.0.6]
	at java.base/java.lang.Thread.run(Thread.java:833) [?:?]
Caused by: org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:292) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:254) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:137) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:500) ~[spring-session-core-3.4.3.jar:3.4.3]
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:524) ~[spring-security-web-6.4.5.jar:6.4.5]
	at com.uino.init.http.response.CustomHttpServletResponseWrapper$ServletOutputStreamWrapper.flush(CustomHttpServletResponseWrapper.java:121) ~[uino-eam-micro-web-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.flush(StandardServletAsyncWebRequest.java:415) ~[spring-web-6.2.6.jar:6.2.6]
	... 138 more
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at java.base/sun.nio.ch.SocketDispatcher.write0(Native Method) ~[?:?]
	at java.base/sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:54) ~[?:?]
	at java.base/sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:132) ~[?:?]
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:97) ~[?:?]
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:53) ~[?:?]
	at java.base/sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:532) ~[?:?]
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:122) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1385) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:763) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:727) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:711) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:563) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:156) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:216) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1258) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:407) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.coyote.Response.action(Response.java:204) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:288) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:254) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:137) ~[tomcat-embed-core-11.0.6.jar:11.0.6]
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:500) ~[spring-session-core-3.4.3.jar:3.4.3]
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:524) ~[spring-security-web-6.4.5.jar:6.4.5]
	at com.uino.init.http.response.CustomHttpServletResponseWrapper$ServletOutputStreamWrapper.flush(CustomHttpServletResponseWrapper.java:121) ~[uino-eam-micro-web-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.flush(StandardServletAsyncWebRequest.java:415) ~[spring-web-6.2.6.jar:6.2.6]
	... 138 more
2025-06-13 14:18:13.605 INFO [http-nio-1515-exec-5] com.uino.init.ControllerAspect : 接收到来自【127.0.0.1】请求，uri为【/tarsier-eam/eam/ciRlt/getAutoDiagramRlt】,请求标识为【null】
2025-06-13 14:18:13.611 INFO [http-nio-1515-exec-5] com.uino.init.ControllerAspect : 参数【rltAutoDiagramVo】为【{"artifactId":3181127342708098,"checkOutLocal":true,"ciCode":"1797209602665552","down":1,"up":1}】
2025-06-13 14:19:35.629 INFO [http-nio-1515-exec-10] com.uino.init.ControllerAspect : 接收到来自【127.0.0.1】请求，uri为【/tarsier-eam/eam/ciRlt/getAutoDiagramRlt】,请求标识为【null】
2025-06-13 14:19:35.633 INFO [http-nio-1515-exec-10] com.uino.init.ControllerAspect : 参数【rltAutoDiagramVo】为【{"artifactId":3181127342708098,"checkOutLocal":true,"ciCode":"1797209602636457","down":1,"up":1}】
2025-06-13 14:19:59.491 INFO [http-nio-1515-exec-1] com.uino.init.ControllerAspect : 接收到来自【127.0.0.1】请求，uri为【/tarsier-eam/eam/ciRlt/getAutoDiagramRlt】,请求标识为【null】
2025-06-13 14:19:59.498 INFO [http-nio-1515-exec-1] com.uino.init.ControllerAspect : 参数【rltAutoDiagramVo】为【{"artifactId":3181127342708098,"checkOutLocal":true,"ciCode":"1797209602636457","down":1,"up":1}】
2025-06-13 14:21:44.074 INFO [http-nio-1515-exec-6] com.uino.init.ControllerAspect : 接收到来自【127.0.0.1】请求，uri为【/tarsier-eam/eam/ciRlt/getAutoDiagramRlt】,请求标识为【null】
2025-06-13 14:21:44.081 INFO [http-nio-1515-exec-6] com.uino.init.ControllerAspect : 参数【rltAutoDiagramVo】为【{"artifactId":3181127342708098,"checkOutLocal":true,"ciCode":"1797209602636457","down":1,"up":1}】
2025-06-13 14:57:28.604 INFO [http-nio-1515-exec-4] com.uino.init.ControllerAspect : 接收到来自【127.0.0.1】请求，uri为【/tarsier-eam/bm/diagram/publishDiagram】,请求标识为【null】
2025-06-13 14:57:28.613 INFO [http-nio-1515-exec-4] com.uino.init.ControllerAspect : 参数【diagramDTO】为【{"diagramId":"3b5076a48966df55","dirId":3181127342787258,"needApprove":true,"releaseDesc":"fgf","shareFlag":false,"targetCiCode":""}】
2025-06-13 14:57:30.092 INFO [http-nio-1515-exec-4] com.uinnova.product.eam.service.es.IamsESCIHistoryDesignSvc : ########## 历史库ciCode：【****************】，对应历史库最大版本号：【2】
2025-06-13 14:57:30.098 INFO [http-nio-1515-exec-4] com.uinnova.product.eam.service.es.IamsESCIHistoryDesignSvc : ########## 历史库ciCode：【3187831825390297】，对应历史库最大版本号：【16】
2025-06-13 14:57:30.371 INFO [http-nio-1515-exec-4] com.uino.dao.AbstractESBaseDao : {"query":{\n  "bool" : {\n    "must" : [\n      {\n        "bool" : {\n          "must" : [\n            {\n              "bool" : {\n                "must" : [\n                  {\n                    "terms" : {\n                      "sourceCiCode.keyword" : [\n                        "3187831825390304"\n                      ],\n                      "boost" : 1.0\n                    }\n                  },\n                  {\n                    "term" : {\n                      "targetCiCode.keyword" : {\n                        "value" : "****************",\n                        "boost" : 1.0\n                      }\n                    }\n                  },\n                  {\n                    "term" : {\n                      "classId" : {\n                        "value" : 3175780809152795,\n                        "boost" : 1.0\n                      }\n                    }\n                  }\n                ],\n                "adjust_pure_negative" : true,\n                "boost" : 1.0\n              }\n            }\n          ],\n          "must_not" : [\n            {\n              "terms" : {\n                "sourceId" : [\n                  1,\n                  2\n                ],\n                "boost" : 1.0\n              }\n            }\n          ],\n          "adjust_pure_negative" : true,\n          "boost" : 1.0\n        }\n      }\n    ],\n    "must_not" : [\n      {\n        "term" : {\n          "dataStatus" : {\n            "value" : 0,\n            "boost" : 1.0\n          }\n        }\n      }\n    ],\n    "adjust_pure_negative" : true,\n    "boost" : 1.0\n  }\n},"script":{"source":"ctx._source.dataStatus=0","lang":"painless"}}
2025-06-13 14:57:30.679 INFO [pool-4-thread-1] com.uinnova.product.eam.service.diagram.impl.ESDiagramExtendSvcImpl : 视图缩略图更新开始:updateIcon->\2025-06-13\d1528477-9f43-4176-a638-605c5e8695ec.png,sourceIcon->/2025-06-13/a896ced0-445b-41cc-8ad0-055063e4db77.png
2025-06-13 14:57:30.684 ERROR[pool-4-thread-1] com.uinnova.product.eam.service.diagram.impl.ESDiagramExtendSvcImpl : 视图缩略图更新失败:updateIcon->\2025-06-13\d1528477-9f43-4176-a638-605c5e8695ec.png,sourceIcon->/2025-06-13/a896ced0-445b-41cc-8ad0-055063e4db77.png
2025-06-13 14:57:30.721 INFO [http-nio-1515-exec-4] com.uinnova.product.eam.service.diagram.impl.ESDiagramExtendSvcImpl : 视图缩略图更新开始:updateIcon->/2025-06-13/a896ced0-445b-41cc-8ad0-055063e4db77.png,sourceIcon->/2025-06-13/c3593eb4-c87f-417c-b4f7-c665697bc574.png
2025-06-13 14:57:30.721 ERROR[http-nio-1515-exec-4] com.uinnova.product.eam.service.diagram.impl.ESDiagramExtendSvcImpl : 视图缩略图更新失败:updateIcon->/2025-06-13/a896ced0-445b-41cc-8ad0-055063e4db77.png,sourceIcon->/2025-06-13/c3593eb4-c87f-417c-b4f7-c665697bc574.png
2025-06-13 14:57:30.805 INFO [http-nio-1515-exec-4] com.uinnova.product.eam.service.diagram.impl.EsDiagramSvcImplV2 : 发布耗时:371
2025-06-13 14:57:30.805 INFO [pool-4-thread-2] com.uinnova.product.eam.service.diagram.impl.ESDiagramExtendSvcImpl : 开始更新
2025-06-13 14:57:30.810 INFO [pool-4-thread-2] com.uinnova.product.eam.service.diagram.impl.ESDiagramExtendSvcImpl : 更新版本成功
2025-06-13 14:57:31.488 INFO [http-nio-1515-exec-4] com.uinnova.product.eam.service.merge.impl.EamMergePostProcessor : ====================发布逻辑后置处理开始=======================
2025-06-13 14:57:31.495 ERROR[http-nio-1515-exec-4] com.uinnova.product.eam.service.merge.impl.EamMergePostProcessor : DM-引用关系分类未创建!
2025-06-13 14:57:31.685 ERROR[http-nio-1515-exec-4] com.uinnova.product.eam.service.merge.impl.EamMergePostProcessor : 归属关系分类未创建!
2025-06-13 14:57:31.688 INFO [http-nio-1515-exec-4] com.uinnova.product.eam.service.merge.impl.EamMergePostProcessor : ====================发布逻辑后置处理结束=======================
2025-06-13 14:57:31.688 INFO [SimpleAsyncTaskExecutor-1] com.uinnova.product.eam.service.dix.api.impl.AppSystemModelSvcImpl : 子系统数据同步开始=======================syncOpen:false,releaseCiList:2
2025-06-13 14:57:31.692 INFO [http-nio-1515-exec-4] com.uinnova.product.eam.service.impl.EamNoticeServiceImpl : 消息：视图信息：[{"attachCiId":0,"colorHistoryMap":"{\"diagram-dts640qv\":{\"rgba(241,243,245,1)\":3,\"rgba(223,227,232,1)\":3,\"rgba(0,0,0,1)\":5},\"diagram-a2882p21\":{\"rgba(165,249,152,1)\":2,\"rgba(163,163,163,1)\":2,\"rgba(0,0,0,1)\":3}}","createTime":20250613145140,"creator":"admin","dEnergy":"3b5076a48966df55","dataStatus":1,"diagramEles":[],"diagramSubType":4,"diagramType":1,"dirId":6417262969550882,"dirType":9,"domainId":1,"historyVersionFlag":1,"icon1":"http://192.168.21.144/rsm/2025-06-13/c3593eb4-c87f-417c-b4f7-c665697bc574.png","id":6590263855500169,"isOpen":0,"leftPanelModel":[{"id":["2"],"opened":true,"originalId":"2","type":"shape"},{"id":["4"],"opened":true,"originalId":"4","type":"shape"}],"localVersion":9,"modifier":"system","modifyTime":20250613145730,"name":"业务方向主视图","ownerCode":"admin","prepareDiagramId":"3b5076a48966df55","releaseDiagramId":"2c9da43a15e00325","releaseVersion":3,"shareRecords":[],"status":1,"subjectId":0,"thumbnailSaveTime":20250613145342,"userId":1,"viewType":"3217840001405079"}]
2025-06-13 14:57:31.695 INFO [http-nio-1515-exec-4] com.uinnova.product.eam.service.impl.EamNoticeServiceImpl : 消息：当前视图无方案关联，结束。params[["3b5076a48966df55"]]
2025-06-13 14:57:42.879 INFO [http-nio-1515-exec-8] com.uino.init.ControllerAspect : 接收到来自【127.0.0.1】请求，uri为【/tarsier-eam/bm/diagram/publishDiagram】,请求标识为【null】
2025-06-13 14:57:42.888 INFO [http-nio-1515-exec-8] com.uino.init.ControllerAspect : 参数【diagramDTO】为【{"diagramId":"3b5076a48966df55","dirId":3181127342787258,"needApprove":true,"releaseDesc":"fgf","shareFlag":false,"targetCiCode":""}】
2025-06-13 14:59:55.314 WARN [Thread-5] com.alibaba.nacos.common.executor.ThreadPoolManager : [ThreadPoolManager] Start destroying ThreadPool
2025-06-13 14:59:55.314 WARN [Thread-16] com.alibaba.nacos.common.http.HttpClientBeanHolder : [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-13 14:59:55.314 WARN [Thread-14] com.alibaba.nacos.common.notify.NotifyCenter : [NotifyCenter] Start destroying Publisher
2025-06-13 14:59:55.314 WARN [Thread-14] com.alibaba.nacos.common.notify.NotifyCenter : [NotifyCenter] Destruction of the end
2025-06-13 14:59:55.315 WARN [Thread-5] com.alibaba.nacos.common.executor.ThreadPoolManager : [ThreadPoolManager] Destruction of the end
2025-06-13 14:59:55.315 WARN [Thread-16] com.alibaba.nacos.common.http.HttpClientBeanHolder : [HttpClientBeanHolder] Destruction of the end
2025-06-13 14:59:55.321 INFO [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-13 14:59:55.352 INFO [http-nio-1515-exec-8] com.uinnova.product.eam.service.es.IamsESCIHistoryDesignSvc : ########## 历史库ciCode：【****************】，对应历史库最大版本号：【3】
2025-06-13 14:59:55.353 INFO [http-nio-1515-exec-8] com.uinnova.product.eam.service.es.IamsESCIHistoryDesignSvc : ########## 历史库ciCode：【3187831825390297】，对应历史库最大版本号：【17】
2025-06-13 14:59:55.623 INFO [http-nio-1515-exec-8] com.uino.dao.AbstractESBaseDao : {"query":{\n  "bool" : {\n    "must" : [\n      {\n        "bool" : {\n          "must" : [\n            {\n              "bool" : {\n                "must" : [\n                  {\n                    "terms" : {\n                      "sourceCiCode.keyword" : [\n                        "3187831825390304"\n                      ],\n                      "boost" : 1.0\n                    }\n                  },\n                  {\n                    "term" : {\n                      "targetCiCode.keyword" : {\n                        "value" : "****************",\n                        "boost" : 1.0\n                      }\n                    }\n                  },\n                  {\n                    "term" : {\n                      "classId" : {\n                        "value" : 3175780809152795,\n                        "boost" : 1.0\n                      }\n                    }\n                  }\n                ],\n                "adjust_pure_negative" : true,\n                "boost" : 1.0\n              }\n            }\n          ],\n          "must_not" : [\n            {\n              "terms" : {\n                "sourceId" : [\n                  1,\n                  2\n                ],\n                "boost" : 1.0\n              }\n            }\n          ],\n          "adjust_pure_negative" : true,\n          "boost" : 1.0\n        }\n      }\n    ],\n    "must_not" : [\n      {\n        "term" : {\n          "dataStatus" : {\n            "value" : 0,\n            "boost" : 1.0\n          }\n        }\n      }\n    ],\n    "adjust_pure_negative" : true,\n    "boost" : 1.0\n  }\n},"script":{"source":"ctx._source.dataStatus=0","lang":"painless"}}
2025-06-13 14:59:55.875 INFO [pool-4-thread-3] com.uinnova.product.eam.service.diagram.impl.ESDiagramExtendSvcImpl : 视图缩略图更新开始:updateIcon->\2025-06-13\10e817e2-9219-42d2-a099-1856d0786079.png,sourceIcon->/2025-06-13/a896ced0-445b-41cc-8ad0-055063e4db77.png
2025-06-13 14:59:55.875 ERROR[pool-4-thread-3] com.uinnova.product.eam.service.diagram.impl.ESDiagramExtendSvcImpl : 视图缩略图更新失败:updateIcon->\2025-06-13\10e817e2-9219-42d2-a099-1856d0786079.png,sourceIcon->/2025-06-13/a896ced0-445b-41cc-8ad0-055063e4db77.png
2025-06-13 14:59:55.948 INFO [http-nio-1515-exec-8] com.uinnova.product.eam.service.diagram.impl.ESDiagramExtendSvcImpl : 视图缩略图更新开始:updateIcon->/2025-06-13/a896ced0-445b-41cc-8ad0-055063e4db77.png,sourceIcon->/2025-06-13/c3593eb4-c87f-417c-b4f7-c665697bc574.png
2025-06-13 14:59:55.948 ERROR[http-nio-1515-exec-8] com.uinnova.product.eam.service.diagram.impl.ESDiagramExtendSvcImpl : 视图缩略图更新失败:updateIcon->/2025-06-13/a896ced0-445b-41cc-8ad0-055063e4db77.png,sourceIcon->/2025-06-13/c3593eb4-c87f-417c-b4f7-c665697bc574.png
2025-06-13 14:59:56.041 INFO [http-nio-1515-exec-8] com.uinnova.product.eam.service.diagram.impl.EsDiagramSvcImplV2 : 发布耗时:365
2025-06-13 14:59:56.041 INFO [pool-4-thread-4] com.uinnova.product.eam.service.diagram.impl.ESDiagramExtendSvcImpl : 开始更新
2025-06-13 14:59:56.044 INFO [pool-4-thread-4] com.uinnova.product.eam.service.diagram.impl.ESDiagramExtendSvcImpl : 更新版本成功
2025-06-13 14:59:56.691 INFO [http-nio-1515-exec-8] com.uinnova.product.eam.service.merge.impl.EamMergePostProcessor : ====================发布逻辑后置处理开始=======================
2025-06-13 14:59:56.698 ERROR[http-nio-1515-exec-8] com.uinnova.product.eam.service.merge.impl.EamMergePostProcessor : DM-引用关系分类未创建!
2025-06-13 14:59:56.890 ERROR[http-nio-1515-exec-8] com.uinnova.product.eam.service.merge.impl.EamMergePostProcessor : 归属关系分类未创建!
2025-06-13 14:59:56.891 INFO [http-nio-1515-exec-8] com.uinnova.product.eam.service.merge.impl.EamMergePostProcessor : ====================发布逻辑后置处理结束=======================
2025-06-13 14:59:56.891 INFO [SimpleAsyncTaskExecutor-2] com.uinnova.product.eam.service.dix.api.impl.AppSystemModelSvcImpl : 子系统数据同步开始=======================syncOpen:false,releaseCiList:2
2025-06-13 14:59:56.893 INFO [http-nio-1515-exec-8] com.uinnova.product.eam.service.impl.EamNoticeServiceImpl : 消息：视图信息：[{"attachCiId":0,"colorHistoryMap":"{\"diagram-dts640qv\":{\"rgba(241,243,245,1)\":3,\"rgba(223,227,232,1)\":3,\"rgba(0,0,0,1)\":5},\"diagram-a2882p21\":{\"rgba(165,249,152,1)\":2,\"rgba(163,163,163,1)\":2,\"rgba(0,0,0,1)\":3}}","createTime":20250613145140,"creator":"admin","dEnergy":"3b5076a48966df55","dataStatus":1,"diagramEles":[],"diagramSubType":4,"diagramType":1,"dirId":6417262969550882,"dirType":9,"domainId":1,"historyVersionFlag":1,"icon1":"http://192.168.21.144/rsm/2025-06-13/c3593eb4-c87f-417c-b4f7-c665697bc574.png","id":6590263855500169,"isOpen":0,"leftPanelModel":[{"id":["2"],"opened":true,"originalId":"2","type":"shape"},{"id":["4"],"opened":true,"originalId":"4","type":"shape"}],"localVersion":9,"modifier":"system","modifyTime":20250613145956,"name":"业务方向主视图","ownerCode":"admin","prepareDiagramId":"3b5076a48966df55","releaseDiagramId":"2c9da43a15e00325","releaseVersion":4,"shareRecords":[],"status":1,"subjectId":0,"thumbnailSaveTime":20250613145342,"userId":1,"viewType":"3217840001405079"}]
2025-06-13 14:59:56.895 INFO [http-nio-1515-exec-8] com.uinnova.product.eam.service.impl.EamNoticeServiceImpl : 消息：当前视图无方案关联，结束。params[["3b5076a48966df55"]]
2025-06-13 14:59:56.958 INFO [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler : Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-06-13 14:59:56.958 INFO [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-13 14:59:56.958 INFO [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler : Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-06-13 14:59:56.963 INFO [SpringApplicationShutdownHook] com.alibaba.cloud.nacos.registry.NacosServiceRegistry : De-registering from Nacos Server now...
2025-06-13 14:59:56.967 INFO [SpringApplicationShutdownHook] com.alibaba.cloud.nacos.registry.NacosServiceRegistry : De-registration finished.
2025-06-13 15:24:36.707 INFO [main] com.uinnova.product.eam.EamApplication : 开始加载eam-web模块
2025-06-13 15:24:37.085 INFO [restartedMain] com.uinnova.product.eam.EamApplication : 开始加载eam-web模块
2025-06-13 15:24:37.719 WARN [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
2025-06-13 15:24:37.720 WARN [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
2025-06-13 15:24:37.975 INFO [restartedMain] com.uinnova.product.eam.EamApplication : Starting EamApplication using Java ******** with PID 48500 (D:\workspace\mycode\ea\eam-app\eam-web\target\classes started by lichong in D:\workspace\mycode\ea\eam-app)
2025-06-13 15:24:37.982 INFO [restartedMain] com.uinnova.product.eam.EamApplication : The following 1 profile is active: "local"
2025-06-13 15:24:44.046 INFO [restartedMain] com.uino.api.init.LocalRunConfig : 发现spring-boot配置为本地加载
2025-06-13 15:24:44.113 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : 开始注册Filter相关信息
2025-06-13 15:24:45.713 WARN [restartedMain] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker : Bean 'redisConfig' of type [com.uinnova.product.eam.config.RedisConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-13 15:24:45.733 INFO [restartedMain] com.uinnova.product.eam.config.RedisConfig : 初始化 -> [Redis CacheErrorHandler]
2025-06-13 15:24:45.734 WARN [restartedMain] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker : Bean 'errorHandler' of type [com.uinnova.product.eam.config.RedisConfig$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-13 15:24:48.322 INFO [restartedMain] org.apache.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-1515"]
2025-06-13 15:24:48.326 INFO [restartedMain] org.apache.catalina.core.StandardService : Starting service [Tomcat]
2025-06-13 15:24:48.327 INFO [restartedMain] org.apache.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/11.0.6]
2025-06-13 15:24:48.619 INFO [restartedMain] org.apache.jasper.servlet.TldScanner : At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-06-13 15:24:48.630 INFO [restartedMain] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/tarsier-eam] : Initializing Spring embedded WebApplicationContext
2025-06-13 15:24:48.660 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : ResponseFilter注册成功
2025-06-13 15:24:48.661 INFO [restartedMain] com.uino.init.http.ResponseFilter : response请求拦截器注册成功
2025-06-13 15:24:48.703 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : RefererFilter注册成功
2025-06-13 15:24:48.710 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : StaticFilter注册成功
2025-06-13 15:24:48.710 INFO [restartedMain] com.uino.init.http.StaticFilter : 静态资源请求拦截器注册成功
2025-06-13 15:24:48.717 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : PermissionFilter注册成功
2025-06-13 15:24:48.725 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : PluginOperateLogFilter注册成功
2025-06-13 15:24:55.753 WARN [restartedMain] com.amazonaws.util.VersionInfoUtils : The AWS SDK for Java 1.x entered maintenance mode starting July 31, 2024 and will reach end of support on December 31, 2025. For more information, see https://aws.amazon.com/blogs/developer/the-aws-sdk-for-java-1-x-is-in-maintenance-mode-effective-july-31-2024/\nYou can print where on the file system the AWS SDK for Java 1.x core runtime is located by setting the AWS_JAVA_V1_PRINT_LOCATION environment variable or aws.java.v1.printLocation system property to 'true'.\nThis message can be disabled by setting the AWS_JAVA_V1_DISABLE_DEPRECATION_ANNOUNCEMENT environment variable or aws.java.v1.disableDeprecationAnnouncement system property to 'true'.\nThe AWS SDK for Java 1.x is being used here:\nat java.base/java.lang.Thread.getStackTrace(Thread.java:1610)\nat com.amazonaws.util.VersionInfoUtils.printDeprecationAnnouncement(VersionInfoUtils.java:81)\nat com.amazonaws.util.VersionInfoUtils.<clinit>(VersionInfoUtils.java:59)\nat com.amazonaws.ClientConfiguration.<clinit>(ClientConfiguration.java:95)\nat com.uino.util.rsm.AliyunObsRsmBehavior.initClient(AliyunObsRsmBehavior.java:48)\nat com.uino.util.rsm.AliyunObsRsmBehavior.<init>(AliyunObsRsmBehavior.java:35)\nat com.uino.util.rsm.RsmUtils.initRsmBehavior(RsmUtils.java:65)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\nat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\nat java.base/java.lang.reflect.Method.invoke(Method.java:568)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:429)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1810)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)\nat org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)\nat org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)\nat org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)\nat org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)\nat org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:318)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)\nat com.uinnova.product.eam.EamApplication.main(EamApplication.java:39)\nat com.uinnova.product.eam.web.test.RunLocal.main(RunLocal.java:10)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\nat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\nat java.base/java.lang.reflect.Method.invoke(Method.java:568)\nat org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
2025-06-13 15:24:58.343 INFO [restartedMain] com.uino.util.cache.config.RedissonConfiguration : RedissonClient, type : single
2025-06-13 15:24:58.359 INFO [restartedMain] com.uino.util.cache.config.RedissonConfiguration : Create redissonClient.
2025-06-13 15:25:03.823 INFO [restartedMain] com.uinnova.product.eam.init.EamLocalRunConfig$$SpringCGLIB$$0 : 发现spring-boot配置为本地加载
2025-06-13 15:25:13.450 INFO [restartedMain] com.uino.init.ControllerAspect : web请求日志切面注册成功
2025-06-13 15:25:13.497 INFO [restartedMain] com.uino.init.ExceptionController : 异常处理类注册成功
2025-06-13 15:25:13.511 INFO [restartedMain] com.uino.init.InitBeans : 开始注册i18-client，修改i18n.files可修改对应国际化文件读取，支持逗号隔开字符串
2025-06-13 15:25:13.526 INFO [restartedMain] com.uino.init.InitBeans : 注册i18-client成功
2025-06-13 15:25:13.527 INFO [restartedMain] com.uinnova.product.devcloud.i18n.client.support.AbstractI18nClient :  has refresh language data ... 
2025-06-13 15:25:13.527 INFO [restartedMain] com.uinnova.product.devcloud.i18n.client.support.AbstractI18nClient :  load refresh language data [114]. 
2025-06-13 15:25:13.596 INFO [restartedMain] com.uino.init.JudgeProcessBeans : 注册校验链
2025-06-13 15:25:14.006 INFO [restartedMain] com.uino.oauth.common.service.UinoLdapAuthenticationProvider : init  ldap user details manager !
2025-06-13 15:25:14.263 INFO [restartedMain] com.uino.monitor.tp.common.SpecifyIndexDao : init SpecifyIndexDao
2025-06-13 15:25:14.264 INFO [restartedMain] com.uino.monitor.tp.common.SpecifyIndexDao : 192.168.21.144:9200;
2025-06-13 15:25:16.078 INFO [restartedMain] com.uino.init.InitBeans : 国际化默认语言为【ZHC】，可通过修改i18n.lang.default修改默认语言，属性范围参考class:Language
2025-06-13 15:25:16.251 INFO [restartedMain] com.uino.init.InitFrameProp : FrameworkProperties注册成功, Local Space http://192.168.21.144/rsm
2025-06-13 15:25:16.813 WARN [restartedMain] org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer$AuthorizationManagerRequestMatcherRegistry : One of the patterns in [/system-setting#/license, ,/system/init/initFlowData, /eam/workbenchChargeDone/openApi/todoCount, /sys/getLogos, /eam/workbenchChargeDone/changeAction, /eam/workbenchChargeDone/saveOrUpdate, /sync/syncUserDataBatchToEa, /permission/oauth/resource/cleanOnlineUser, /wiki/getTokenByCode, /login/getLoginMethod, /cj/system/diagram/changeFlowByDiagramIds, /planDesign/updatePlanDiagramIsFlow, /redirectAuth, /getTokenByCode, /refreshToken, /cmdb/dataSet/execute, /eam/user/getUserByRoleName, /cmdb/dataSet/realTimeExecute, /websocket/*/*, /eam/oauth/getTokenByLoginInfo, /trial/saas/login/check, /eam/notice/workflow/msg/save, /planDesign/getPlanForFeign, /planDesign/findRenewVersionPlanList, /flowable/getApprovalUser, /flowable/approval/task, /flowable/batchModifyWorkbenchTask, /rsm/**, /ai/api/**, /webjars/css/*.css, /webjars/js/*.js, /swagger-resources, /v2/api-docs, /doc.html] is missing a leading slash. This is discouraged; please include the leading slash in all your request matcher patterns. In future versions of Spring Security, leaving out the leading slash will result in an exception.
2025-06-13 15:25:20.440 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Using default implementation for ThreadExecutor
2025-06-13 15:25:20.454 INFO [restartedMain] org.quartz.core.SchedulerSignalerImpl : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-13 15:25:20.455 INFO [restartedMain] org.quartz.core.QuartzScheduler : Quartz Scheduler v.2.3.2 created.
2025-06-13 15:25:20.455 INFO [restartedMain] org.quartz.simpl.RAMJobStore : RAMJobStore initialized.
2025-06-13 15:25:20.457 INFO [restartedMain] org.quartz.core.QuartzScheduler : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'\n  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.\n  NOT STARTED.\n  Currently in standby mode.\n  Number of jobs executed: 0\n  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.\n  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.\n
2025-06-13 15:25:20.457 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-13 15:25:20.457 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Quartz scheduler version: 2.3.2
2025-06-13 15:25:20.457 INFO [restartedMain] org.quartz.core.QuartzScheduler : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@66c389f3
2025-06-13 15:25:22.096 INFO [restartedMain] org.apache.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-1515"]
2025-06-13 15:25:23.464 INFO [restartedMain] com.alibaba.cloud.nacos.registry.NacosServiceRegistry : nacos registry, DEFAULT_GROUP eam-fuxi 192.168.205.1:1515 register finished
2025-06-13 15:25:23.590 INFO [restartedMain] org.quartz.core.QuartzScheduler : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-13 15:25:23.656 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 15:25:23.658 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 15:25:24.874 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 15:25:25.012 INFO [restartedMain] com.uinnova.product.eam.EamApplication : Started EamApplication in 47.921 seconds (process running for 49.939)
2025-06-13 15:25:25.027 INFO [restartedMain] com.uino.service.util.FileUtil : FileUtil Initialization method called
2025-06-13 15:25:25.047 INFO [restartedMain] com.uinnova.product.eam.web.mix.diagram.v2.webSocekt.DiagramServer : DiagramServer start success
2025-06-13 15:25:25.047 INFO [restartedMain] com.uinnova.product.eam.init.FlowSystemDataInit : 流程体系数据初始化开关关闭
2025-06-13 15:25:25.234 INFO [restartedMain] com.uinnova.product.eam.service.utils.ConvertBusinessKeyUtil : 转换businessKey: false
2025-06-13 15:25:25.234 INFO [restartedMain] com.uino.service.util.FileUtil : FileUtil Initialization method called
2025-06-13 15:25:25.238 INFO [restartedMain] com.uinnova.product.eam.EamApplication : EAM application start success :)
2025-06-13 15:25:44.021 INFO [http-nio-1515-exec-1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/tarsier-eam] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 15:25:44.302 INFO [http-nio-1515-exec-1] com.uino.init.ControllerAspect : 接收到来自【127.0.0.1】请求，uri为【/tarsier-eam/bm/diagram/publishDiagram】,请求标识为【null】
2025-06-13 15:25:44.319 INFO [http-nio-1515-exec-1] com.uino.init.ControllerAspect : 参数【diagramDTO】为【{"diagramId":"3b5076a48966df55","dirId":3181127342787258,"needApprove":true,"releaseDesc":"fgf","shareFlag":false,"targetCiCode":""}】
2025-06-13 15:27:51.466 INFO [http-nio-1515-exec-1] com.uinnova.product.eam.service.es.IamsESCIHistoryDesignSvc : ########## 历史库ciCode：【****************】，对应历史库最大版本号：【4】
2025-06-13 15:27:51.473 INFO [http-nio-1515-exec-1] com.uinnova.product.eam.service.es.IamsESCIHistoryDesignSvc : ########## 历史库ciCode：【3187831825390297】，对应历史库最大版本号：【18】
2025-06-13 15:27:56.142 INFO [http-nio-1515-exec-1] com.uino.dao.AbstractESBaseDao : {"query":{\n  "bool" : {\n    "must" : [\n      {\n        "bool" : {\n          "must" : [\n            {\n              "bool" : {\n                "must" : [\n                  {\n                    "terms" : {\n                      "sourceCiCode.keyword" : [\n                        "3187831825390304"\n                      ],\n                      "boost" : 1.0\n                    }\n                  },\n                  {\n                    "term" : {\n                      "targetCiCode.keyword" : {\n                        "value" : "****************",\n                        "boost" : 1.0\n                      }\n                    }\n                  },\n                  {\n                    "term" : {\n                      "classId" : {\n                        "value" : 3175780809152795,\n                        "boost" : 1.0\n                      }\n                    }\n                  }\n                ],\n                "adjust_pure_negative" : true,\n                "boost" : 1.0\n              }\n            }\n          ],\n          "must_not" : [\n            {\n              "terms" : {\n                "sourceId" : [\n                  1,\n                  2\n                ],\n                "boost" : 1.0\n              }\n            }\n          ],\n          "adjust_pure_negative" : true,\n          "boost" : 1.0\n        }\n      }\n    ],\n    "must_not" : [\n      {\n        "term" : {\n          "dataStatus" : {\n            "value" : 0,\n            "boost" : 1.0\n          }\n        }\n      }\n    ],\n    "adjust_pure_negative" : true,\n    "boost" : 1.0\n  }\n},"script":{"source":"ctx._source.dataStatus=0","lang":"painless"}}
2025-06-13 15:33:17.714 WARN [Thread-5] com.alibaba.nacos.common.executor.ThreadPoolManager : [ThreadPoolManager] Start destroying ThreadPool
2025-06-13 15:33:17.714 WARN [Thread-16] com.alibaba.nacos.common.http.HttpClientBeanHolder : [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-13 15:33:17.714 WARN [Thread-14] com.alibaba.nacos.common.notify.NotifyCenter : [NotifyCenter] Start destroying Publisher
2025-06-13 15:33:17.714 WARN [Thread-14] com.alibaba.nacos.common.notify.NotifyCenter : [NotifyCenter] Destruction of the end
2025-06-13 15:33:17.714 WARN [Thread-5] com.alibaba.nacos.common.executor.ThreadPoolManager : [ThreadPoolManager] Destruction of the end
2025-06-13 15:33:17.715 WARN [Thread-16] com.alibaba.nacos.common.http.HttpClientBeanHolder : [HttpClientBeanHolder] Destruction of the end
2025-06-13 15:33:17.720 INFO [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-13 15:33:17.735 INFO [http-nio-1515-exec-1] com.uino.dao.AbstractESBaseDao : {"query":{\n  "bool" : {\n    "must" : [\n      {\n        "terms" : {\n          "ciCode.keyword" : [\n            "****************"\n          ],\n          "boost" : 1.0\n        }\n      },\n      {\n        "term" : {\n          "ownerCode.keyword" : {\n            "value" : "admin",\n            "boost" : 1.0\n          }\n        }\n      }\n    ],\n    "adjust_pure_negative" : true,\n    "boost" : 1.0\n  }\n},"script":{"source":"ctx._source.publicVersion+=1;ctx._source.localVersion=0","lang":"painless"}}
2025-06-13 15:46:07.918 INFO [main] com.uinnova.product.eam.EamApplication : 开始加载eam-web模块
2025-06-13 15:46:08.692 INFO [restartedMain] com.uinnova.product.eam.EamApplication : 开始加载eam-web模块
2025-06-13 15:46:10.176 WARN [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
2025-06-13 15:46:10.179 WARN [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
2025-06-13 15:46:10.622 INFO [restartedMain] com.uinnova.product.eam.EamApplication : Starting EamApplication using Java ******** with PID 42788 (D:\workspace\mycode\ea\eam-app\eam-web\target\classes started by lichong in D:\workspace\mycode\ea\eam-app)
2025-06-13 15:46:10.628 INFO [restartedMain] com.uinnova.product.eam.EamApplication : The following 1 profile is active: "local"
2025-06-13 15:46:18.718 INFO [restartedMain] com.uino.api.init.LocalRunConfig : 发现spring-boot配置为本地加载
2025-06-13 15:46:18.782 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : 开始注册Filter相关信息
2025-06-13 15:46:21.270 WARN [restartedMain] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker : Bean 'redisConfig' of type [com.uinnova.product.eam.config.RedisConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-13 15:46:21.318 INFO [restartedMain] com.uinnova.product.eam.config.RedisConfig : 初始化 -> [Redis CacheErrorHandler]
2025-06-13 15:46:21.326 WARN [restartedMain] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker : Bean 'errorHandler' of type [com.uinnova.product.eam.config.RedisConfig$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-13 15:46:23.974 INFO [restartedMain] org.apache.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-1515"]
2025-06-13 15:46:23.977 INFO [restartedMain] org.apache.catalina.core.StandardService : Starting service [Tomcat]
2025-06-13 15:46:23.977 INFO [restartedMain] org.apache.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/11.0.6]
2025-06-13 15:46:24.204 INFO [restartedMain] org.apache.jasper.servlet.TldScanner : At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-06-13 15:46:24.212 INFO [restartedMain] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/tarsier-eam] : Initializing Spring embedded WebApplicationContext
2025-06-13 15:46:24.227 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : ResponseFilter注册成功
2025-06-13 15:46:24.228 INFO [restartedMain] com.uino.init.http.ResponseFilter : response请求拦截器注册成功
2025-06-13 15:46:24.254 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : RefererFilter注册成功
2025-06-13 15:46:24.258 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : StaticFilter注册成功
2025-06-13 15:46:24.259 INFO [restartedMain] com.uino.init.http.StaticFilter : 静态资源请求拦截器注册成功
2025-06-13 15:46:24.264 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : PermissionFilter注册成功
2025-06-13 15:46:24.270 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : PluginOperateLogFilter注册成功
2025-06-13 15:46:30.116 WARN [restartedMain] com.amazonaws.util.VersionInfoUtils : The AWS SDK for Java 1.x entered maintenance mode starting July 31, 2024 and will reach end of support on December 31, 2025. For more information, see https://aws.amazon.com/blogs/developer/the-aws-sdk-for-java-1-x-is-in-maintenance-mode-effective-july-31-2024/\nYou can print where on the file system the AWS SDK for Java 1.x core runtime is located by setting the AWS_JAVA_V1_PRINT_LOCATION environment variable or aws.java.v1.printLocation system property to 'true'.\nThis message can be disabled by setting the AWS_JAVA_V1_DISABLE_DEPRECATION_ANNOUNCEMENT environment variable or aws.java.v1.disableDeprecationAnnouncement system property to 'true'.\nThe AWS SDK for Java 1.x is being used here:\nat java.base/java.lang.Thread.getStackTrace(Thread.java:1610)\nat com.amazonaws.util.VersionInfoUtils.printDeprecationAnnouncement(VersionInfoUtils.java:81)\nat com.amazonaws.util.VersionInfoUtils.<clinit>(VersionInfoUtils.java:59)\nat com.amazonaws.ClientConfiguration.<clinit>(ClientConfiguration.java:95)\nat com.uino.util.rsm.AliyunObsRsmBehavior.initClient(AliyunObsRsmBehavior.java:48)\nat com.uino.util.rsm.AliyunObsRsmBehavior.<init>(AliyunObsRsmBehavior.java:35)\nat com.uino.util.rsm.RsmUtils.initRsmBehavior(RsmUtils.java:65)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\nat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\nat java.base/java.lang.reflect.Method.invoke(Method.java:568)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:429)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1810)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)\nat org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)\nat org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)\nat org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)\nat org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)\nat org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:318)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)\nat com.uinnova.product.eam.EamApplication.main(EamApplication.java:39)\nat com.uinnova.product.eam.web.test.RunLocal.main(RunLocal.java:10)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\nat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\nat java.base/java.lang.reflect.Method.invoke(Method.java:568)\nat org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
2025-06-13 15:46:32.018 INFO [restartedMain] com.uino.util.cache.config.RedissonConfiguration : RedissonClient, type : single
2025-06-13 15:46:32.030 INFO [restartedMain] com.uino.util.cache.config.RedissonConfiguration : Create redissonClient.
2025-06-13 15:46:37.167 INFO [restartedMain] com.uinnova.product.eam.init.EamLocalRunConfig$$SpringCGLIB$$0 : 发现spring-boot配置为本地加载
2025-06-13 15:46:44.550 INFO [restartedMain] com.uino.init.ControllerAspect : web请求日志切面注册成功
2025-06-13 15:46:44.584 INFO [restartedMain] com.uino.init.ExceptionController : 异常处理类注册成功
2025-06-13 15:46:44.610 INFO [restartedMain] com.uino.init.InitBeans : 开始注册i18-client，修改i18n.files可修改对应国际化文件读取，支持逗号隔开字符串
2025-06-13 15:46:44.624 INFO [restartedMain] com.uino.init.InitBeans : 注册i18-client成功
2025-06-13 15:46:44.625 INFO [restartedMain] com.uinnova.product.devcloud.i18n.client.support.AbstractI18nClient :  has refresh language data ... 
2025-06-13 15:46:44.625 INFO [restartedMain] com.uinnova.product.devcloud.i18n.client.support.AbstractI18nClient :  load refresh language data [114]. 
2025-06-13 15:46:44.690 INFO [restartedMain] com.uino.init.JudgeProcessBeans : 注册校验链
2025-06-13 15:46:45.089 INFO [restartedMain] com.uino.oauth.common.service.UinoLdapAuthenticationProvider : init  ldap user details manager !
2025-06-13 15:46:45.325 INFO [restartedMain] com.uino.monitor.tp.common.SpecifyIndexDao : init SpecifyIndexDao
2025-06-13 15:46:45.326 INFO [restartedMain] com.uino.monitor.tp.common.SpecifyIndexDao : 192.168.21.144:9200;
2025-06-13 15:46:46.884 INFO [restartedMain] com.uino.init.InitBeans : 国际化默认语言为【ZHC】，可通过修改i18n.lang.default修改默认语言，属性范围参考class:Language
2025-06-13 15:46:47.009 INFO [restartedMain] com.uino.init.InitFrameProp : FrameworkProperties注册成功, Local Space http://192.168.21.144/rsm
2025-06-13 15:46:47.383 WARN [restartedMain] org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer$AuthorizationManagerRequestMatcherRegistry : One of the patterns in [/system-setting#/license, ,/system/init/initFlowData, /eam/workbenchChargeDone/openApi/todoCount, /sys/getLogos, /eam/workbenchChargeDone/changeAction, /eam/workbenchChargeDone/saveOrUpdate, /sync/syncUserDataBatchToEa, /permission/oauth/resource/cleanOnlineUser, /wiki/getTokenByCode, /login/getLoginMethod, /cj/system/diagram/changeFlowByDiagramIds, /planDesign/updatePlanDiagramIsFlow, /redirectAuth, /getTokenByCode, /refreshToken, /cmdb/dataSet/execute, /eam/user/getUserByRoleName, /cmdb/dataSet/realTimeExecute, /websocket/*/*, /eam/oauth/getTokenByLoginInfo, /trial/saas/login/check, /eam/notice/workflow/msg/save, /planDesign/getPlanForFeign, /planDesign/findRenewVersionPlanList, /flowable/getApprovalUser, /flowable/approval/task, /flowable/batchModifyWorkbenchTask, /rsm/**, /ai/api/**, /webjars/css/*.css, /webjars/js/*.js, /swagger-resources, /v2/api-docs, /doc.html] is missing a leading slash. This is discouraged; please include the leading slash in all your request matcher patterns. In future versions of Spring Security, leaving out the leading slash will result in an exception.
2025-06-13 15:46:50.463 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Using default implementation for ThreadExecutor
2025-06-13 15:46:50.473 INFO [restartedMain] org.quartz.core.SchedulerSignalerImpl : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-13 15:46:50.473 INFO [restartedMain] org.quartz.core.QuartzScheduler : Quartz Scheduler v.2.3.2 created.
2025-06-13 15:46:50.474 INFO [restartedMain] org.quartz.simpl.RAMJobStore : RAMJobStore initialized.
2025-06-13 15:46:50.475 INFO [restartedMain] org.quartz.core.QuartzScheduler : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'\n  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.\n  NOT STARTED.\n  Currently in standby mode.\n  Number of jobs executed: 0\n  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.\n  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.\n
2025-06-13 15:46:50.475 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-13 15:46:50.475 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Quartz scheduler version: 2.3.2
2025-06-13 15:46:50.475 INFO [restartedMain] org.quartz.core.QuartzScheduler : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4c666c20
2025-06-13 15:46:51.686 INFO [restartedMain] org.apache.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-1515"]
2025-06-13 15:46:52.746 INFO [restartedMain] com.alibaba.cloud.nacos.registry.NacosServiceRegistry : nacos registry, DEFAULT_GROUP eam-fuxi 192.168.205.1:1515 register finished
2025-06-13 15:46:52.873 INFO [restartedMain] org.quartz.core.QuartzScheduler : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-13 15:46:52.928 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 15:46:52.930 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 15:46:54.041 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 15:46:54.150 INFO [restartedMain] com.uinnova.product.eam.EamApplication : Started EamApplication in 45.442 seconds (process running for 48.716)
2025-06-13 15:46:54.163 INFO [restartedMain] com.uino.service.util.FileUtil : FileUtil Initialization method called
2025-06-13 15:46:54.181 INFO [restartedMain] com.uino.service.util.FileUtil : FileUtil Initialization method called
2025-06-13 15:46:54.182 INFO [restartedMain] com.uinnova.product.eam.service.utils.ConvertBusinessKeyUtil : 转换businessKey: false
2025-06-13 15:46:54.308 INFO [restartedMain] com.uinnova.product.eam.web.mix.diagram.v2.webSocekt.DiagramServer : DiagramServer start success
2025-06-13 15:46:54.311 INFO [restartedMain] com.uinnova.product.eam.init.FlowSystemDataInit : 流程体系数据初始化开关关闭
2025-06-13 15:46:54.314 INFO [restartedMain] com.uinnova.product.eam.EamApplication : EAM application start success :)
2025-06-13 15:47:12.324 WARN [Thread-16] com.alibaba.nacos.common.http.HttpClientBeanHolder : [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-13 15:47:12.324 WARN [Thread-14] com.alibaba.nacos.common.notify.NotifyCenter : [NotifyCenter] Start destroying Publisher
2025-06-13 15:47:12.324 WARN [Thread-5] com.alibaba.nacos.common.executor.ThreadPoolManager : [ThreadPoolManager] Start destroying ThreadPool
2025-06-13 15:47:12.325 WARN [Thread-14] com.alibaba.nacos.common.notify.NotifyCenter : [NotifyCenter] Destruction of the end
2025-06-13 15:47:12.325 WARN [Thread-5] com.alibaba.nacos.common.executor.ThreadPoolManager : [ThreadPoolManager] Destruction of the end
2025-06-13 15:47:12.325 WARN [Thread-16] com.alibaba.nacos.common.http.HttpClientBeanHolder : [HttpClientBeanHolder] Destruction of the end
2025-06-13 15:47:12.328 INFO [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-06-13 15:49:07.386 INFO [main] com.uinnova.product.eam.EamApplication : 开始加载eam-web模块
2025-06-13 15:49:08.017 INFO [restartedMain] com.uinnova.product.eam.EamApplication : 开始加载eam-web模块
2025-06-13 15:49:09.068 WARN [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
2025-06-13 15:49:09.070 WARN [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
2025-06-13 15:49:09.380 INFO [restartedMain] com.uinnova.product.eam.EamApplication : Starting EamApplication using Java ******** with PID 17936 (D:\workspace\mycode\ea\eam-app\eam-web\target\classes started by lichong in D:\workspace\mycode\ea\eam-app)
2025-06-13 15:49:09.389 INFO [restartedMain] com.uinnova.product.eam.EamApplication : The following 1 profile is active: "local"
2025-06-13 15:49:15.206 INFO [restartedMain] com.uino.api.init.LocalRunConfig : 发现spring-boot配置为本地加载
2025-06-13 15:49:15.260 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : 开始注册Filter相关信息
2025-06-13 15:49:16.511 WARN [restartedMain] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker : Bean 'redisConfig' of type [com.uinnova.product.eam.config.RedisConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-13 15:49:16.526 INFO [restartedMain] com.uinnova.product.eam.config.RedisConfig : 初始化 -> [Redis CacheErrorHandler]
2025-06-13 15:49:16.527 WARN [restartedMain] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker : Bean 'errorHandler' of type [com.uinnova.product.eam.config.RedisConfig$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-13 15:49:18.275 INFO [restartedMain] org.apache.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-1515"]
2025-06-13 15:49:18.276 INFO [restartedMain] org.apache.catalina.core.StandardService : Starting service [Tomcat]
2025-06-13 15:49:18.277 INFO [restartedMain] org.apache.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/11.0.6]
2025-06-13 15:49:18.482 INFO [restartedMain] org.apache.jasper.servlet.TldScanner : At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-06-13 15:49:18.490 INFO [restartedMain] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/tarsier-eam] : Initializing Spring embedded WebApplicationContext
2025-06-13 15:49:18.507 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : ResponseFilter注册成功
2025-06-13 15:49:18.508 INFO [restartedMain] com.uino.init.http.ResponseFilter : response请求拦截器注册成功
2025-06-13 15:49:18.536 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : RefererFilter注册成功
2025-06-13 15:49:18.540 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : StaticFilter注册成功
2025-06-13 15:49:18.541 INFO [restartedMain] com.uino.init.http.StaticFilter : 静态资源请求拦截器注册成功
2025-06-13 15:49:18.546 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : PermissionFilter注册成功
2025-06-13 15:49:18.553 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : PluginOperateLogFilter注册成功
2025-06-13 15:49:24.411 WARN [restartedMain] com.amazonaws.util.VersionInfoUtils : The AWS SDK for Java 1.x entered maintenance mode starting July 31, 2024 and will reach end of support on December 31, 2025. For more information, see https://aws.amazon.com/blogs/developer/the-aws-sdk-for-java-1-x-is-in-maintenance-mode-effective-july-31-2024/\nYou can print where on the file system the AWS SDK for Java 1.x core runtime is located by setting the AWS_JAVA_V1_PRINT_LOCATION environment variable or aws.java.v1.printLocation system property to 'true'.\nThis message can be disabled by setting the AWS_JAVA_V1_DISABLE_DEPRECATION_ANNOUNCEMENT environment variable or aws.java.v1.disableDeprecationAnnouncement system property to 'true'.\nThe AWS SDK for Java 1.x is being used here:\nat java.base/java.lang.Thread.getStackTrace(Thread.java:1610)\nat com.amazonaws.util.VersionInfoUtils.printDeprecationAnnouncement(VersionInfoUtils.java:81)\nat com.amazonaws.util.VersionInfoUtils.<clinit>(VersionInfoUtils.java:59)\nat com.amazonaws.ClientConfiguration.<clinit>(ClientConfiguration.java:95)\nat com.uino.util.rsm.AliyunObsRsmBehavior.initClient(AliyunObsRsmBehavior.java:48)\nat com.uino.util.rsm.AliyunObsRsmBehavior.<init>(AliyunObsRsmBehavior.java:35)\nat com.uino.util.rsm.RsmUtils.initRsmBehavior(RsmUtils.java:65)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\nat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\nat java.base/java.lang.reflect.Method.invoke(Method.java:568)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)\nat org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:429)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1810)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1681)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1739)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1627)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:599)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:577)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:739)\nat org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:272)\nat org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)\nat org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:369)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1451)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)\nat org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)\nat org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)\nat org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:371)\nat org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)\nat org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1221)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1187)\nat org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1122)\nat org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)\nat org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)\nat org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)\nat org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)\nat org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:318)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)\nat org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)\nat com.uinnova.product.eam.EamApplication.main(EamApplication.java:39)\nat com.uinnova.product.eam.web.test.RunLocal.main(RunLocal.java:10)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\nat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\nat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\nat java.base/java.lang.reflect.Method.invoke(Method.java:568)\nat org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
2025-06-13 15:49:26.375 INFO [restartedMain] com.uino.util.cache.config.RedissonConfiguration : RedissonClient, type : single
2025-06-13 15:49:26.392 INFO [restartedMain] com.uino.util.cache.config.RedissonConfiguration : Create redissonClient.
2025-06-13 15:49:31.146 INFO [restartedMain] com.uinnova.product.eam.init.EamLocalRunConfig$$SpringCGLIB$$0 : 发现spring-boot配置为本地加载
2025-06-13 15:49:40.047 INFO [restartedMain] com.uino.init.ControllerAspect : web请求日志切面注册成功
2025-06-13 15:49:40.089 INFO [restartedMain] com.uino.init.ExceptionController : 异常处理类注册成功
2025-06-13 15:49:40.104 INFO [restartedMain] com.uino.init.InitBeans : 开始注册i18-client，修改i18n.files可修改对应国际化文件读取，支持逗号隔开字符串
2025-06-13 15:49:40.121 INFO [restartedMain] com.uino.init.InitBeans : 注册i18-client成功
2025-06-13 15:49:40.122 INFO [restartedMain] com.uinnova.product.devcloud.i18n.client.support.AbstractI18nClient :  has refresh language data ... 
2025-06-13 15:49:40.122 INFO [restartedMain] com.uinnova.product.devcloud.i18n.client.support.AbstractI18nClient :  load refresh language data [114]. 
2025-06-13 15:49:40.190 INFO [restartedMain] com.uino.init.JudgeProcessBeans : 注册校验链
2025-06-13 15:49:40.638 INFO [restartedMain] com.uino.oauth.common.service.UinoLdapAuthenticationProvider : init  ldap user details manager !
2025-06-13 15:49:40.894 INFO [restartedMain] com.uino.monitor.tp.common.SpecifyIndexDao : init SpecifyIndexDao
2025-06-13 15:49:40.896 INFO [restartedMain] com.uino.monitor.tp.common.SpecifyIndexDao : 192.168.21.144:9200;
2025-06-13 15:49:42.707 INFO [restartedMain] com.uino.init.InitBeans : 国际化默认语言为【ZHC】，可通过修改i18n.lang.default修改默认语言，属性范围参考class:Language
2025-06-13 15:49:42.885 INFO [restartedMain] com.uino.init.InitFrameProp : FrameworkProperties注册成功, Local Space http://192.168.21.144/rsm
2025-06-13 15:49:43.390 WARN [restartedMain] org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer$AuthorizationManagerRequestMatcherRegistry : One of the patterns in [/system-setting#/license, ,/system/init/initFlowData, /eam/workbenchChargeDone/openApi/todoCount, /sys/getLogos, /eam/workbenchChargeDone/changeAction, /eam/workbenchChargeDone/saveOrUpdate, /sync/syncUserDataBatchToEa, /permission/oauth/resource/cleanOnlineUser, /wiki/getTokenByCode, /login/getLoginMethod, /cj/system/diagram/changeFlowByDiagramIds, /planDesign/updatePlanDiagramIsFlow, /redirectAuth, /getTokenByCode, /refreshToken, /cmdb/dataSet/execute, /eam/user/getUserByRoleName, /cmdb/dataSet/realTimeExecute, /websocket/*/*, /eam/oauth/getTokenByLoginInfo, /trial/saas/login/check, /eam/notice/workflow/msg/save, /planDesign/getPlanForFeign, /planDesign/findRenewVersionPlanList, /flowable/getApprovalUser, /flowable/approval/task, /flowable/batchModifyWorkbenchTask, /rsm/**, /ai/api/**, /webjars/css/*.css, /webjars/js/*.js, /swagger-resources, /v2/api-docs, /doc.html] is missing a leading slash. This is discouraged; please include the leading slash in all your request matcher patterns. In future versions of Spring Security, leaving out the leading slash will result in an exception.
2025-06-13 15:49:47.079 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Using default implementation for ThreadExecutor
2025-06-13 15:49:47.093 INFO [restartedMain] org.quartz.core.SchedulerSignalerImpl : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-13 15:49:47.094 INFO [restartedMain] org.quartz.core.QuartzScheduler : Quartz Scheduler v.2.3.2 created.
2025-06-13 15:49:47.094 INFO [restartedMain] org.quartz.simpl.RAMJobStore : RAMJobStore initialized.
2025-06-13 15:49:47.095 INFO [restartedMain] org.quartz.core.QuartzScheduler : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'\n  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.\n  NOT STARTED.\n  Currently in standby mode.\n  Number of jobs executed: 0\n  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.\n  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.\n
2025-06-13 15:49:47.095 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-13 15:49:47.095 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Quartz scheduler version: 2.3.2
2025-06-13 15:49:47.095 INFO [restartedMain] org.quartz.core.QuartzScheduler : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@17963178
2025-06-13 15:49:48.551 INFO [restartedMain] org.apache.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-1515"]
2025-06-13 15:49:50.036 INFO [restartedMain] com.alibaba.cloud.nacos.registry.NacosServiceRegistry : nacos registry, DEFAULT_GROUP eam-fuxi 192.168.205.1:1515 register finished
2025-06-13 15:49:50.182 INFO [restartedMain] org.quartz.core.QuartzScheduler : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-13 15:49:50.258 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 15:49:50.260 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 15:49:51.488 WARN [restartedMain] org.springframework.context.support.SimpleThreadScope : SimpleThreadScope does not support destruction callbacks. Consider using RequestScope in a web environment.
2025-06-13 15:49:51.629 INFO [restartedMain] com.uinnova.product.eam.EamApplication : Started EamApplication in 43.599 seconds (process running for 46.621)
2025-06-13 15:49:51.810 INFO [restartedMain] com.uino.service.util.FileUtil : FileUtil Initialization method called
2025-06-13 15:49:51.810 INFO [restartedMain] com.uinnova.product.eam.web.mix.diagram.v2.webSocekt.DiagramServer : DiagramServer start success
2025-06-13 15:49:51.810 INFO [restartedMain] com.uinnova.product.eam.init.FlowSystemDataInit : 流程体系数据初始化开关关闭
2025-06-13 15:49:51.813 INFO [restartedMain] com.uinnova.product.eam.service.utils.ConvertBusinessKeyUtil : 转换businessKey: false
2025-06-13 15:49:51.813 INFO [restartedMain] com.uino.service.util.FileUtil : FileUtil Initialization method called
2025-06-13 15:49:51.817 INFO [restartedMain] com.uinnova.product.eam.EamApplication : EAM application start success :)
2025-06-13 15:50:05.545 INFO [http-nio-1515-exec-1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/tarsier-eam] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 15:50:05.765 INFO [http-nio-1515-exec-1] com.uino.init.ControllerAspect : 接收到来自【127.0.0.1】请求，uri为【/tarsier-eam/bm/diagram/publishDiagram】,请求标识为【null】
2025-06-13 15:50:05.778 INFO [http-nio-1515-exec-1] com.uino.init.ControllerAspect : 参数【diagramDTO】为【{"diagramId":"3b5076a48966df55","dirId":3181127342787258,"needApprove":true,"releaseDesc":"fgf","shareFlag":false,"targetCiCode":""}】
2025-06-13 15:50:14.643 INFO [http-nio-1515-exec-1] com.uinnova.product.eam.service.es.IamsESCIHistoryDesignSvc : ########## 历史库ciCode：【****************】，对应历史库最大版本号：【5】
2025-06-13 15:50:14.650 INFO [http-nio-1515-exec-1] com.uinnova.product.eam.service.es.IamsESCIHistoryDesignSvc : ########## 历史库ciCode：【3187831825390297】，对应历史库最大版本号：【19】
2025-06-13 15:51:02.741 INFO [pool-4-thread-1] com.uinnova.product.eam.service.diagram.impl.ESDiagramExtendSvcImpl : 视图缩略图更新开始:updateIcon->\2025-06-13\6435711e-75cc-47df-a110-d91547c652f1.png,sourceIcon->/2025-06-13/a896ced0-445b-41cc-8ad0-055063e4db77.png
2025-06-13 15:51:02.745 ERROR[pool-4-thread-1] com.uinnova.product.eam.service.diagram.impl.ESDiagramExtendSvcImpl : 视图缩略图更新失败:updateIcon->\2025-06-13\6435711e-75cc-47df-a110-d91547c652f1.png,sourceIcon->/2025-06-13/a896ced0-445b-41cc-8ad0-055063e4db77.png
2025-06-13 15:51:02.783 INFO [http-nio-1515-exec-1] com.uinnova.product.eam.service.diagram.impl.ESDiagramExtendSvcImpl : 视图缩略图更新开始:updateIcon->/2025-06-13/a896ced0-445b-41cc-8ad0-055063e4db77.png,sourceIcon->/2025-06-13/c3593eb4-c87f-417c-b4f7-c665697bc574.png
2025-06-13 15:51:02.784 ERROR[http-nio-1515-exec-1] com.uinnova.product.eam.service.diagram.impl.ESDiagramExtendSvcImpl : 视图缩略图更新失败:updateIcon->/2025-06-13/a896ced0-445b-41cc-8ad0-055063e4db77.png,sourceIcon->/2025-06-13/c3593eb4-c87f-417c-b4f7-c665697bc574.png
2025-06-13 15:51:02.889 INFO [http-nio-1515-exec-1] com.uinnova.product.eam.service.diagram.impl.EsDiagramSvcImplV2 : 发布耗时:506
2025-06-13 15:51:02.889 INFO [pool-4-thread-2] com.uinnova.product.eam.service.diagram.impl.ESDiagramExtendSvcImpl : 开始更新
2025-06-13 15:51:02.894 INFO [pool-4-thread-2] com.uinnova.product.eam.service.diagram.impl.ESDiagramExtendSvcImpl : 更新版本成功
2025-06-13 15:51:03.635 INFO [http-nio-1515-exec-1] com.uinnova.product.eam.service.merge.impl.EamMergePostProcessor : ====================发布逻辑后置处理开始=======================
2025-06-13 15:51:03.643 ERROR[http-nio-1515-exec-1] com.uinnova.product.eam.service.merge.impl.EamMergePostProcessor : DM-引用关系分类未创建!
2025-06-13 15:51:03.852 ERROR[http-nio-1515-exec-1] com.uinnova.product.eam.service.merge.impl.EamMergePostProcessor : 归属关系分类未创建!
2025-06-13 15:51:03.857 INFO [http-nio-1515-exec-1] com.uinnova.product.eam.service.merge.impl.EamMergePostProcessor : ====================发布逻辑后置处理结束=======================
2025-06-13 15:51:03.858 INFO [SimpleAsyncTaskExecutor-1] com.uinnova.product.eam.service.dix.api.impl.AppSystemModelSvcImpl : 子系统数据同步开始=======================syncOpen:false,releaseCiList:2
2025-06-13 15:51:03.863 INFO [http-nio-1515-exec-1] com.uinnova.product.eam.service.impl.EamNoticeServiceImpl : 消息：视图信息：[{"attachCiId":0,"colorHistoryMap":"{\"diagram-dts640qv\":{\"rgba(241,243,245,1)\":3,\"rgba(223,227,232,1)\":3,\"rgba(0,0,0,1)\":5},\"diagram-a2882p21\":{\"rgba(165,249,152,1)\":2,\"rgba(163,163,163,1)\":2,\"rgba(0,0,0,1)\":3}}","createTime":20250613145140,"creator":"admin","dEnergy":"3b5076a48966df55","dataStatus":1,"diagramEles":[],"diagramSubType":4,"diagramType":1,"dirId":6417262969550882,"dirType":9,"domainId":1,"historyVersionFlag":1,"icon1":"http://192.168.21.144/rsm/2025-06-13/c3593eb4-c87f-417c-b4f7-c665697bc574.png","id":6590263855500169,"isOpen":0,"leftPanelModel":[{"id":["2"],"opened":true,"originalId":"2","type":"shape"},{"id":["4"],"opened":true,"originalId":"4","type":"shape"}],"localVersion":12,"modifier":"system","modifyTime":20250613155102,"name":"业务方向主视图","ownerCode":"admin","prepareDiagramId":"3b5076a48966df55","releaseDiagramId":"2c9da43a15e00325","releaseVersion":5,"shareRecords":[],"status":1,"subjectId":0,"thumbnailSaveTime":20250613151030,"userId":1,"viewType":"3217840001405079"}]
2025-06-13 15:51:03.866 INFO [http-nio-1515-exec-1] com.uinnova.product.eam.service.impl.EamNoticeServiceImpl : 消息：当前视图无方案关联，结束。params[["3b5076a48966df55"]]
2025-06-13 15:54:21.741 WARN [Thread-14] com.alibaba.nacos.common.notify.NotifyCenter : [NotifyCenter] Start destroying Publisher
2025-06-13 15:54:21.741 WARN [Thread-16] com.alibaba.nacos.common.http.HttpClientBeanHolder : [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-13 15:54:21.741 WARN [Thread-5] com.alibaba.nacos.common.executor.ThreadPoolManager : [ThreadPoolManager] Start destroying ThreadPool
2025-06-13 15:54:21.742 WARN [Thread-14] com.alibaba.nacos.common.notify.NotifyCenter : [NotifyCenter] Destruction of the end
2025-06-13 15:54:21.742 WARN [Thread-5] com.alibaba.nacos.common.executor.ThreadPoolManager : [ThreadPoolManager] Destruction of the end
2025-06-13 15:54:21.742 WARN [Thread-16] com.alibaba.nacos.common.http.HttpClientBeanHolder : [HttpClientBeanHolder] Destruction of the end
2025-06-13 15:54:21.747 INFO [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
