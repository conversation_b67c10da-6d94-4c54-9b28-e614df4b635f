package com.uinnova.product.eam.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.binary.core.exception.BinaryException;
import com.binary.core.exception.MessageException;
import com.binary.core.i18n.LanguageResolver;
import com.binary.core.io.Compression;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.binary.jdbc.Page;
import com.binary.json.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.uinnova.product.eam.base.util.ExcelUtil;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.comm.model.es.EamDirectoryObjectAssociation;
import com.uinnova.product.eam.comm.model.es.FlowSystemAssociatedFeatures;
import com.uinnova.product.eam.comm.bean.AppSquareConfigVO;
import com.uinnova.product.eam.comm.model.es.WorkbenchChargeDone;
import com.uinnova.product.eam.model.CiQueryCdtExtend;
import com.uinnova.product.eam.model.EamArtifactCiVo;
import com.uinnova.product.eam.model.EamArtifactElementVo;
import com.uinnova.product.eam.model.dto.CiCodeDto;
import com.uinnova.product.eam.model.enums.ArtifactType;
import com.uinnova.product.eam.model.vo.ESCISearchBeanVO;
import com.uinnova.product.eam.model.vo.ExportCiVO;
import com.uinnova.product.eam.service.*;
import com.uinnova.product.eam.service.diagram.threadlocal.ThreadVariable;
import com.uinnova.product.eam.service.es.*;
import com.uinnova.product.eam.service.utils.AssetManageUtil;
import com.uinnova.product.eam.service.asset.AssetContent;
import com.uinnova.product.vmdb.comm.bean.CIState;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.comm.util.CiExcelUtil;
import com.uinnova.product.vmdb.comm.util.CommUtil;
import com.uinnova.product.vmdb.provider.ci.bean.*;
import com.uinnova.product.vmdb.provider.search.bean.CcCiSearchPage;
import com.uino.bean.cmdb.base.*;
import com.uino.bean.cmdb.business.*;
import com.uino.bean.cmdb.enums.AttrNameKeyEnum;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESAttrBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysRoleDataModuleRlt;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.CIExportLabel;
import com.uino.bean.permission.query.CSysRoleDataModuleRlt;
import com.uino.bean.permission.query.CSysUser;
import com.uino.bean.sys.base.ESCIOperateLog;
import com.uino.bean.sys.base.ESDictionaryItemInfo;
import com.uino.bean.sys.query.ESDictionaryItemSearchBean;
import com.uino.dao.BaseConst;
import com.uino.dao.cmdb.*;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.ICISvc;
import com.uino.service.cmdb.microservice.IRltClassSvc;
import com.uino.service.cmdb.microservice.impl.CIClassSvc;
import com.uino.service.permission.data.CIViewPermission;
import com.uino.service.permission.microservice.IUserSvc;
import com.uino.service.permission.microservice.impl.RoleSvc;
import com.uino.service.sys.microservice.ICIOperateLogSvc;
import com.uino.service.sys.microservice.IDictionarySvc;
import com.uino.service.sys.microservice.IResourceSvc;
import com.uino.service.util.FileUtil;
import com.uino.util.excel.EasyExcelUtil;
import com.uino.util.rsm.RsmUtils;
import com.uino.util.sys.BeanUtil;
import com.uino.util.sys.CheckAttrUtil;
import com.uino.util.sys.SysUtil;
import com.uino.util.sys.ValidDtoUtil;
import lombok.SneakyThrows;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Deprecated
@Service
public class IamsCIDesignSvc implements ICISvc, AssetContent {

    private static final Logger log = LoggerFactory.getLogger(IamsCIDesignSvc.class);

    public static final String CI_CODE = "ciCode";
    @Autowired
    private IUserSvc userSvc;

    @Autowired
    private IamsESCIDesignSvc esCiSvc;

    @Autowired
    private ESImageSvc esImageSvc;

    @Autowired
    private ESCIClassSvc esClsSvc;

    @Autowired
    private CIClassSvc clsSvc;

    @Resource
    private IRltClassSvc iRltClassSvc;

    @Resource
    private  IamsESCIRltDesignSvc esCiRltSvc;

    @Autowired
    private IamsCIRltDesignSvc ciRltSvc;

    @Autowired
    @Lazy
    private IamsESCmdbCommDesignSvc commSvc;

    @Autowired
    private IResourceSvc resourceSvc;

    @Autowired
    private ICIOperateLogSvc logSvc;

    @Autowired
    private IamsESCIHistoryDesignSvc esHistorySvc;

    @Autowired
    private IEamCIClassApiSvc classApiSvc;

    @Autowired
    private ESCIAttrTransConfigSvc attrTransConfigSvc;

    @Autowired
    private ESCIHistorySvc ciHistorySvc;

    @Autowired
    private RoleSvc roleSvc;

    @Autowired
    private IDictionarySvc dictSvc;

    @Autowired
    private IamsCIDesignNonComplianceDao iamsCIDesignNonComplianceDao;

    @Autowired
    private RsmUtils rsmUtils;

    @Resource
    private FlowSystemAssociatedFeaturesDao flowSystemAssociatedFeaturesDao;

    @Value("${is.show.3d.attribute:false}")
    private Boolean isShow3dAttribute;

    @Value("${http.resource.space}")
    private String urlPath;

    @Value("${local.resource.space}")
    private String localPath;

    @Autowired
    private IEamArtifactColumnSvc artifactColumnSvc;
    @Autowired
    ICISwitchSvc ciSwitchSvc;

    @Resource
    private WorkbenchChargeDoneSvc workbenchChargeDoneSvc;

    @Resource
    private AppSquareConfigSvc appSquareConfigSvc;

    @Resource
    private EamCategorySvc categorySvc;

    @Resource
    private EamDirectoryObjectAssociationDao eamDirectoryObjectAssociationDao;

    @Override
    public Long saveOrUpdate(CcCiInfo ciInfo) {
        CcCi ci = ciInfo.getCi();
        Long classId = ciInfo.getCi().getClassId();
        String ciCode = ciInfo.getCi().getCiCode();
        Map<String, String> attrs = ciInfo.getAttrs();
        Assert.notNull(ci, "X_PARAM_NOT_NULL${name:ci}");
        Assert.notNull(classId, "X_PARAM_NOT_NULL${name:classId}");
        SysUser loginUser = null;
        try {
            loginUser = ThreadVariable.getSysUser();
            if (loginUser == null) {
                loginUser = SysUtil.getCurrentUserInfo();
            }
        } catch (Exception e) {
            log.error("获取登陆用户失败，所有值取默认值");
        }
        boolean isAdd = BinaryUtils.isEmpty(ci.getId()) ? true : false;
        String ownerCode = BinaryUtils.isEmpty(ci.getOwnerCode()) ? (loginUser == null ? "system" : loginUser.getLoginCode()) : ci.getOwnerCode();
        // 分类是否存在
        //        List<CcCiAttrDef> attrDefs = esClsSvc.getAllDefsByClassId(domainId, classId);
        List<ESCIClassInfo> classInfoList = classApiSvc.selectCiClassByIds(Lists.newArrayList(classId));
        if (CollectionUtils.isEmpty(classInfoList)) {
            throw MessageException.i18n("BS_MNAME_CLASS_NOT_EXSIT");
        }
        ESCIClassInfo ciClass = classInfoList.get(0);
        // 校验CI属性-当前及父类属性
        String classStdCode = ciClass.getClassStdCode();
        List<ESCIAttrDefInfo> attrEsDefs = ciClass.getAttrDefs();
        Map<String, ESCIAttrDefInfo> attrDefInfoMap = attrEsDefs.stream().collect(Collectors.toMap(e -> e.getProStdName(), e -> e, (k1, k2) -> k1));
        List<CcCiAttrDef> attrDefs = BeanUtil.converBean(attrEsDefs, CcCiAttrDef.class);
        //循环attrDefs获取关联资产构建关系的id
        Set<Long> rltIds = new HashSet<>();
        for (CcCiAttrDef attrDef : attrDefs) {
            if (attrDef.getProType() == ESPropertyType.LINK_CI.getValue()) {
                if (attrDef.getConstructRltId() != null) {
                    rltIds.add(attrDef.getConstructRltId());
                }
            }
        }
        //查询所有关系信息
        Map<Long, CcCiClassInfo> rltClassMap = new HashMap<>();
        if (!BinaryUtils.isEmpty(rltIds)) {
            CCcCiClass cdt = new CCcCiClass();
            cdt.setIds(rltIds.toArray(new Long[rltIds.size()]));
            List<CcCiClassInfo> rltClasses = iRltClassSvc.getRltClassByCdt(cdt);
            rltClassMap = rltClasses.stream().collect(Collectors.toMap(ccCiClassInfo -> ccCiClassInfo.getCiClass().getId(), ccCiClassInfo -> ccCiClassInfo, (k1, k2) -> k1));
        }   

        // When the 3D model is opened, save the CI and add the path conversion
        this.removeModelIconPath(attrDefs, attrs);

        Map<String, Integer> checkResult = commSvc.validAttrs(attrDefs, attrs, true);
        if (!BinaryUtils.isEmpty(checkResult)) {
            for (String result : checkResult.keySet()) {
                throw new MessageException(result);
            }
        }
        getEnCodeNumByDef(ciClass.getClassCode(), attrDefs, attrs);
        Map<String, String> stdMap = toStdMap(attrs);
        // 校验数据字典类型
        Map<String, List<String>> dictValuesMap = this.getExterDictValues(BaseConst.DEFAULT_DOMAIN_ID, attrDefs);
        if (!BinaryUtils.isEmpty(dictValuesMap)) {
            Iterator<Entry<String, List<String>>> it = dictValuesMap.entrySet().iterator();
            while (it.hasNext()) {
                Entry<String, List<String>> next = it.next();
                String key = next.getKey();
                List<String> values = next.getValue();
                String val = stdMap.get(key);
                ESCIAttrDefInfo esciAttrDefInfo = attrDefInfoMap.get(key);
                List<String> group = esciAttrDefInfo.getGroup();
                if (!CollectionUtils.isEmpty(group) && group.contains(CHECKBOX)) {
                    if (!BinaryUtils.isEmpty(val)) {
                        boolean flag = false;
                        String[] vals = new String[0];
                        try {
                            vals = val.split(MULT_SYMBOL);
                        } catch (Exception e) {
                            flag = true;
                            log.error("属性[" + key + "]引用值[" + val + "]数据格式异常，多选为数组格式");
                        }
                        for (String value : vals) {
                            if (!BinaryUtils.isEmpty(value) && !values.contains(value)) {
                                flag = true;
                                break;
                            }
                        }
                        if (flag) {
                            throw new IllegalArgumentException("属性[" + key + "]引用值[" + val + "]为多选格式，数据格式异常");
                        }
                    }
                } else {
                    Assert.isTrue(!isAdd || BinaryUtils.isEmpty(val) || values.contains(val), "属性[" + key + "]引用值[" + val + "]不存在");
                }
            }
        }

        // 校验重复
        List<String> ciPKAttrDefNames = CommUtil.getCiPKAttrDefNames(attrDefs);
        Integer hashCode = CommUtil.getCiMajorHashCode(attrs, ciPKAttrDefNames, classStdCode);
        List<String> ciPrimaryKeys = CommUtil.getCiPrimaryKeys(classStdCode, attrs, ciPKAttrDefNames);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("hashCode", hashCode));
        boolQuery.must(QueryBuilders.termQuery("classId", ciClass.getId()));
        boolQuery.must(QueryBuilders.termQuery("domainId", 1L));
        List<CcCiInfo> liveCis = esCiSvc.getCIInfoPageByQuery(1, 3000, boolQuery, false).getData();
        for (CcCiInfo cInfo : liveCis) {
            if (compareCiPrimaryKeys(ciPrimaryKeys, JSON.toList(cInfo.getCi().getCiPrimaryKey(), String.class))) {
                // 同分类下ciCode或id相同，更新，否则删除
                // 有id时优先按id更新，兼容仅有ciCode时更新的情况
                boolean idEqual = ci.getId() != null && ci.getId().longValue() == cInfo.getCi().getId().longValue();
                boolean codeEqual = ciCode != null && ciCode.equals(cInfo.getCi().getCiCode());
                boolean classIdEqual = ci.getClassId().longValue() == cInfo.getCi().getClassId().longValue();
                boolean isUpdate = (codeEqual || idEqual) && classIdEqual;
                if (isUpdate) {
                    if (ci.getId() != null && ciCode != null) {
                        if(!codeEqual || !idEqual){
                            throw new BinaryException("[设计库]数据已存在,主键冲突-:" + ciPrimaryKeys);
                        }
                        //Assert.isTrue(codeEqual && idEqual, "BS_CI_NO_EXIST");
                    }
                    ci.setId(cInfo.getCi().getId());
                    isAdd = false;
                } else {
                    throw new BinaryException("[设计库]数据已存在,主键冲突:" + ciPrimaryKeys);
//                    throw MessageException.i18n("BS_MNAME_RECORD_CONTAINS");
                }
            }
        }

        if (isAdd && !BinaryUtils.isEmpty(ciCode)) {
            List<ESCIInfo> ciCodeQuery = esCiSvc.getListByQuery(QueryBuilders.termQuery("ciCode.keyword", ciCode));
            if (!BinaryUtils.isEmpty(ciCodeQuery)) {
                ci.setId(ciCodeQuery.get(0).getId());
                isAdd = false;
            }
        }
        // 填充资产状态等默认值
        fillAttrDefaultVal(ciInfo, stdMap,attrDefs);
        // 属性过滤，只保存定义过的属性
        Iterator<String> itAttr = stdMap.keySet().iterator();
        List<String> defNames = attrDefs.stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList());
        while (itAttr.hasNext()) {
            String attrName = itAttr.next();
            // 属性不包含在定义的属性，并且不是下划线开头结尾的字段，去掉
            if (!defNames.contains(attrName) && !attrName.matches("_([\\d\\D]*?)_")) {
                itAttr.remove();
            }
        }
        ciInfo.setAttrs(stdMap);
        Map<String, String> oldAttrs = null;
        // 组装CI
        if (isAdd) {
            long uuid = ESUtil.getUUID();
            ci.setId(uuid);
            ci.setCiCode(BinaryUtils.isEmpty(ciCode) ? String.valueOf(uuid) : ciCode);
            if (BinaryUtils.isEmpty(ci.getCreator())) {
                ci.setCreator(loginUser == null ? "system" : loginUser.getLoginCode());
            }
            ci.setCreateTime(ESUtil.getNumberDateTime());
            ci.setCiVersion(String.valueOf(1));
            ci.setLocalVersion(0);
            ci.setPublicVersion(1L);
            ci.setProcessCoding(ciInfo.getAttrs().get("流程编码")==null?ciInfo.getAttrs().get("流程组编码"):ciInfo.getAttrs().get("流程编码"));
            //当目录id不为0，则将CI放入文件目录下
            if (ciInfo.getDirId()!=null) {
                putObjectsUnderFolders(ciInfo);
            }
        } else {
            CcCiInfo dbCIInfo = esCiSvc.getCiInfoById(ci.getId());
            Assert.notNull(dbCIInfo, "BS_CI_NO_EXIST");
            oldAttrs = dbCIInfo.getAttrs();
            ci.setCiCode(dbCIInfo.getCi().getCiCode());
            ci.setCreateTime(dbCIInfo.getCi().getCreateTime());
            ci.setCiVersion(dbCIInfo.getCi().getCiVersion());
            ci.setLocalVersion(0L);
            ci.setProcessCoding(ciInfo.getAttrs().get("流程编码")==null?ciInfo.getAttrs().get("流程组编码"):ciInfo.getAttrs().get("流程编码"));

            ci.setPublicVersion(dbCIInfo.getCi().getPublicVersion());
            if (!CheckAttrUtil.checkAttrMapEqual(stdMap, oldAttrs)) {
                ci.setCiVersion(String.valueOf(Long.parseLong(dbCIInfo.getCi().getCiVersion()) + 1L));
                ci.setPublicVersion(dbCIInfo.getCi().getPublicVersion() + 1L);
            }
        }
        ci.setClassId(classId);
        ci.setOwnerCode(ownerCode);
        ci.setDataStatus(1);
        ci.setHashCode(hashCode);
        ci.setCiPrimaryKey(JSON.toString(ciPrimaryKeys));
        Long id1 = esCiSvc.saveOrUpdateCI(ciInfo);
        if (isAdd) {
            this.saveCIOperateLog(null, SysUtil.StaticUtil.LOG_DYNAMIC_INSERT, attrDefs, null, stdMap, ciClass.getClassName(), ci);
        } else {
            this.saveCIOperateLog(null, SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE, attrDefs, oldAttrs, stdMap, ciClass.getClassName(), ci);
        }
        ciInfo.setAttrDefs(attrDefs);
        createLinkCIRlts(ciInfo,oldAttrs,rltClassMap);
        createWorkbenchChargeDone(ciInfo,LibType.DESIGN);
        return id1;
    }

    public void putObjectsUnderFolders(CcCiInfo ciInfo) {
        String newCiCode = ciInfo.getCi().getCiCode();
            EamCategory parent  = categorySvc.getById(ciInfo.getDirId(), LibType.DESIGN);
            if (parent == null){
                throw new BinaryException("无此目录,请重新选择目录!");
            }else {
                EamDirectoryObjectAssociation eamDirectoryObjectAssociation = new EamDirectoryObjectAssociation();
                eamDirectoryObjectAssociation.setId(ESUtil.getUUID());
                eamDirectoryObjectAssociation.setCiCode(newCiCode);
                eamDirectoryObjectAssociation.setDirectoryId(parent.getId());
                eamDirectoryObjectAssociationDao.saveOrUpdate(eamDirectoryObjectAssociation);
            }

    }

    @SneakyThrows
    public void createWorkbenchChargeDone(CcCiInfo ciInfo,LibType libType) {

        String jsonString = ciInfo.getAttrs().get("编写人");
        if (StringUtils.isBlank(jsonString)){
            return;
        }
         ciInfo = ciSwitchSvc.getCiInfoById(ciInfo.getCi().getId(), libType);
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(jsonString);
        List<WorkbenchChargeDone> workbenchChargeDones = new ArrayList<>();
        for (JsonNode node : rootNode) {
            String loginCode = node.get("loginCode").asText();
            if (StringUtils.isNotBlank(loginCode)){
                WorkbenchChargeDone chargeDone=new WorkbenchChargeDone();
                chargeDone.setType(5);
                chargeDone.setAction(1);
                chargeDone.setBusinessId(ciInfo.getCi().getId().toString());
                chargeDone.setBusinessKey(ciInfo.getCi().getCiCode());
                chargeDone.setTaskId(ciInfo.getCi().getCiCode());
                chargeDone.setUserId(loginCode);
                chargeDone.setSubmitter(SysUtil.getCurrentUserInfo().getLoginCode());
                chargeDone.setCurrentAssignees(loginCode);
                String processInstanceName = ciInfo.getAttrs().get("流程名称");
                String processInstanceCode = ciInfo.getAttrs().get("流程编码");
                if (BinaryUtils.isEmpty(processInstanceName) || BinaryUtils.isEmpty(processInstanceCode)) {
                    log.error("数据属性信息缺少流程编码或流程名称信息ciCode：{}，attrs{}",
                            ciInfo.getCi().getCiCode(),
                            JSONObject.toJSONString(ciInfo.getAttrs()));
                    chargeDone.setProcessInstanceName("您为当前流程的编写人，请进入流程体系维护流程");
                } else {
                    chargeDone.setProcessInstanceName("您为" + processInstanceCode + " " + processInstanceName + "编写人，请进入流程体系维护流程");
                }
                chargeDone.setTaskCreateTime(new Date());
                workbenchChargeDones.add(chargeDone);
            }
        }
        workbenchChargeDoneSvc.saveOrUpdate(workbenchChargeDones);
    }


    private void createLinkCIRlts(CcCiInfo ciInfo,Map<String, String> oldAttrs,Map<Long, CcCiClassInfo> rltClassMap) {
         List<ESCIRltInfo> rltInfoList = parseLinkCIRlts(ciInfo, oldAttrs,rltClassMap);
        if (!CollectionUtils.isEmpty(rltInfoList)) {
            ciRltSvc.bindBatchCiRlt(rltInfoList, SysUtil.getCurrentUserInfo().getLoginCode());
        }
    }

    /**
     * 解析当前ci需要创建的关系数据
     * @param ciInfo
     * @param oldAttrs
     * @return
     */
    List<ESCIRltInfo> parseLinkCIRlts(CcCiInfo ciInfo, Map<String, String> oldAttrs,Map<Long, CcCiClassInfo> rltClassMap){
        List<CcCiAttrDef> attrDefs = ciInfo.getAttrDefs();
        Map<String, String> attrs = ciInfo.getAttrs();
        List<ESCIRltInfo> rltInfoList = new ArrayList<>();
        Set<String> ciCodes = new HashSet<>();
        for (CcCiAttrDef attrDef : attrDefs) {
            if (attrDef.getProType() == ESPropertyType.LINK_CI.getValue()) {
                String sourceOrTarget = attrDef.getSourceOrTarget();
                Long constructRltId = attrDef.getConstructRltId();
                if (org.apache.commons.lang3.StringUtils.isBlank(sourceOrTarget) || constructRltId == null) {
                    continue;
                }
                CcCiClassInfo linkClass = rltClassMap.get(constructRltId);
                String linkCiStringData = attrs.get(attrDef.getProStdName());
                Set<String> deleteCiCodes = new HashSet<>();
                if (org.apache.commons.lang3.StringUtils.isNotBlank(linkCiStringData)) {
                    JSONArray ciList = com.alibaba.fastjson.JSON.parseArray(linkCiStringData);
                    if (oldAttrs != null) {
                        String oldLinkCiStringData = oldAttrs.get(attrDef.getProStdName());
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(oldLinkCiStringData)) {
                            JSONArray oldCiList = com.alibaba.fastjson.JSON.parseArray(oldLinkCiStringData);
                            Set<String> newCiCodeSet = new HashSet<>();
                            for (int i = 0; i < ciList.size(); i++) {
                                JSONObject jsonObject = ciList.getJSONObject(i);
                                String ciCode = jsonObject.getString("ciCode");
                                newCiCodeSet.add(ciCode);
                            }
                            for (int i = 0; i < oldCiList.size(); i++) {
                                JSONObject jsonObject = oldCiList.getJSONObject(i);
                                String ciCode = jsonObject.getString("ciCode");
                                if (!newCiCodeSet.contains(ciCode)) {
                                    deleteCiCodes.add(ciCode);
                                }
                            }
                        }
                    }
                    for (int i = 0; i < ciList.size(); i++) {
                        JSONObject jsonObject = ciList.getJSONObject(i);
                        String ciCode = jsonObject.getString("ciCode");
                        ESCIRltInfo esciRltInfo = new ESCIRltInfo();
                        esciRltInfo.setClassId(linkClass.getCiClass().getId());
                        if ("target".equalsIgnoreCase(sourceOrTarget)) {
                            String key = ciInfo.getCi().getCiCode() + "_" + linkClass.getCiClass().getId() + "_" + ciCode;
                            esciRltInfo.setCiCode(key);
                            esciRltInfo.setUniqueCode("UK_" + key);
                            esciRltInfo.setSourceCiCode(ciInfo.getCi().getCiCode());
                            esciRltInfo.setSourceClassId(ciInfo.getCi().getClassId());
                            esciRltInfo.setTargetCiCode(ciCode);
                            esciRltInfo.setTargetClassId(Long.parseLong(attrDef.getProDropSourceDef()));
                        } else {
                            String key = ciCode + "_" + linkClass.getCiClass().getId() + "_" + ciInfo.getCi().getCiCode();
                            esciRltInfo.setCiCode(key);
                            esciRltInfo.setUniqueCode("UK_" + key);
                            esciRltInfo.setSourceCiCode(ciCode);
                            esciRltInfo.setSourceClassId(Long.parseLong(attrDef.getProDropSourceDef()));
                            esciRltInfo.setTargetCiCode(ciInfo.getCi().getCiCode());
                            esciRltInfo.setTargetClassId(ciInfo.getCi().getClassId());
                        }
                        ciCodes.add(ciCode);
                        rltInfoList.add(esciRltInfo);
                    }
                } else {
                    if (oldAttrs != null) {
                        String oldLinkCiStringData = oldAttrs.get(attrDef.getProStdName());
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(oldLinkCiStringData)) {
                            JSONArray oldCiList = com.alibaba.fastjson.JSON.parseArray(oldLinkCiStringData);
                            for (int i = 0; i < oldCiList.size(); i++) {
                                JSONObject jsonObject = oldCiList.getJSONObject(i);
                                String ciCode = jsonObject.getString("ciCode");
                                deleteCiCodes.add(ciCode);
                            }
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(deleteCiCodes)) {
                    BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
                    if ("source".equalsIgnoreCase(sourceOrTarget)) {
                        boolQueryBuilder.must(QueryBuilders.termsQuery("sourceCiCode.keyword", deleteCiCodes));
                        boolQueryBuilder.must(QueryBuilders.termQuery("targetCiCode.keyword", ciInfo.getCi().getCiCode()));
                    } else {
                        boolQueryBuilder.must(QueryBuilders.termQuery("sourceCiCode.keyword", ciInfo.getCi().getCiCode()));
                        boolQueryBuilder.must(QueryBuilders.termsQuery("targetCiCode.keyword", deleteCiCodes));
                    }
                    //boolQueryBuilder.must(QueryBuilders.termQuery("ownerCode.keyword", SysUtil.getCurrentUserInfo().getLoginCode()));
                    boolQueryBuilder.must(QueryBuilders.termQuery("classId", constructRltId));
                    esCiRltSvc.deleteByQuery(boolQueryBuilder, true);
                }
            }
        }
        return rltInfoList;
    }


    private void fillAttrDefaultVal(CcCiInfo ciInfo, Map<String, String> stdMap, List<CcCiAttrDef> attrDefs) {
        boolean present = attrDefs.stream().anyMatch(e -> ATTR_DEFAULT_LIST.contains(e.getProName()));
        if (present) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat timeFormatter = new SimpleDateFormat("yyyyMMddHHmmss");
            if (BinaryUtils.isEmpty(stdMap.get(RELEASE_STATE))) {
                stdMap.put(RELEASE_STATE, RELEASE);
            }
            String createTime = null;
            String modifyTime = null;
            try {
                Long cicreateTime = ciInfo.getCi().getCreateTime();
                if (null == cicreateTime) {
                    cicreateTime = ESUtil.getNumberDateTime();
                }
                createTime = sdf.format(timeFormatter.parse(cicreateTime.toString()));
                Long ciModifyTime = ciInfo.getCi().getModifyTime();
                if (null == ciModifyTime) {
                    ciModifyTime = BinaryUtils.getNumberDateTime();
                }
                modifyTime = sdf.format(timeFormatter.parse(ciModifyTime.toString()));
                if (BinaryUtils.isEmpty(stdMap.get(RELEASE_TIME))) {
                    stdMap.put(RELEASE_TIME, createTime);
                }
                if (BinaryUtils.isEmpty(stdMap.get(CREATION_TIME))) {
                    stdMap.put(CREATION_TIME, createTime);
                }
                if (BinaryUtils.isEmpty(stdMap.get(MODIFI_TIME))) {
                    stdMap.put(MODIFI_TIME, modifyTime);
                }
            } catch (Exception e) {
                log.error("时间转换异常，保存资产属性");
            }
        }
    }

    @Override
    public Long saveOrUpdate(CcCiInfo ciInfo, SaveType saveType) {
        return null;
    }

    public void getEnCodeNumByDefStr(List<CcCiInfo> ciList) {
        Set<Long> classIdList = ciList.stream().map(CcCiInfo::getCi).map(CcCi::getClassId).collect(Collectors.toSet());
        List<ESCIClassInfo> ciClassInfoPage = esClsSvc.getListByQuery(1, classIdList.size(), QueryBuilders.termsQuery("classId", classIdList)).getData();
        Map<Long, ESCIClassInfo> classMap = ciClassInfoPage.stream().collect(Collectors.toMap(ESCIClassInfo::getId, each -> each, (k1, k2) -> k2));
        for (CcCiInfo ci : ciList) {
            ESCIClassInfo ciClassInfo = classMap.get(ci.getCi().getClassId());
            getEnCodeNumByDef(ciClassInfo.getClassCode(), ciClassInfo.getCcAttrDefs(), ci.getAttrs());
        }
    }

    public <T> void getEnCodeNumByDef(String classCode, List<CcCiAttrDef> defs, Map<String, T> attrs) {
        List<CcCiAttrDef> pros = defs.stream().filter(each -> each.getProType().intValue() == 150).collect(Collectors.toList());
        CiCodeDto ciCodeDto = new CiCodeDto();
        ciCodeDto.setClassCode(classCode);
        for (CcCiAttrDef pro : pros) {
            if(pro.getDefVal().equals(attrs.get(pro.getProName())) || BinaryUtils.isEmpty(attrs.get(pro.getProName()))){
                ciCodeDto.setDefVal(pro.getDefVal());
                ciCodeDto.setProName(pro.getProName());
                ciCodeDto.setStartNum(pro.getEnumValues());
                String num = classApiSvc.getEnCodeNum(ciCodeDto);
                attrs.put(pro.getProName(), (T)num);
            }
        }

        ArrayList<Integer> integers = new ArrayList<>();
        integers.add(20);
        integers.add(21);
        List<CcCiAttrDef> numCodePros = defs.stream().filter(each -> integers.contains(each.getProType()) ).collect(Collectors.toList());
        CiCodeDto ciIntegerCodeDto = new CiCodeDto();
        ciIntegerCodeDto.setClassCode(classCode);
        if(attrs == null){
            attrs = new HashMap<>();
        }
        for (CcCiAttrDef pro : numCodePros) {
            if(pro.getDefVal().equals(attrs.get(pro.getProName())) || BinaryUtils.isEmpty(attrs.get(pro.getProName()))){
                ciIntegerCodeDto.setDefVal(pro.getDefVal());
                ciIntegerCodeDto.setAttrDefId(pro.getId());
                ciIntegerCodeDto.setStartNum(pro.getEnumValues());
                String num = classApiSvc.getIntegerEnCodeNum(ciIntegerCodeDto)+"";
                attrs.put(pro.getProName(), (T)num);
            }
        }

    }

    @Override
    public Long saveOrUpdateExtra(CcCiInfo ciInfo) {
        CcCi ci = ciInfo.getCi();
        if (ci.getDomainId() == null) {
            ci.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        Long classId = ciInfo.getCi().getClassId();
        String ciCode = ciInfo.getCi().getCiCode();
        Map<String, String> attrs = ciInfo.getAttrs();
        Assert.notNull(ci, "X_PARAM_NOT_NULL${name:ci}");
        Assert.notNull(classId, "X_PARAM_NOT_NULL${name:classId}");
        SysUser loginUser = null;
        try {
            loginUser = SysUtil.getCurrentUserInfo();
        } catch (Exception e) {
            log.error("获取登陆用户失败，所有值取默认值");
        }
        boolean isAdd = BinaryUtils.isEmpty(ci.getId());
        // 分类是否存在
        ESCIClassInfo ciClass = esClsSvc.getById(classId);
        if (ciClass == null) {
            throw MessageException.i18n("BS_MNAME_CLASS_NOT_EXSIT");
        }
        // 校验CI属性-当前及父类属性
        List<CcCiAttrDef> attrDefs = esClsSvc.getAllDefsByClassId(ci.getDomainId(), classId);

        // When the 3D model is opened, save the CI and add the path conversion
        this.removeModelIconPath(attrDefs, attrs);

        Map<String, Integer> checkResult = commSvc.validAttrs(attrDefs, attrs, true);
        if (!BinaryUtils.isEmpty(checkResult)) {
            throw new MessageException(checkResult.keySet().toArray()[0].toString());
        }

        Map<String, String> stdMap = toStdMap(attrs);
        // 校验数据字典类型
        Map<String, List<String>> dictValuesMap = this.getExterDictValues(ciInfo.getCi().getDomainId(), attrDefs);
        if (!BinaryUtils.isEmpty(dictValuesMap)) {
            for (Entry<String, List<String>> next : dictValuesMap.entrySet()) {
                String key = next.getKey();
                List<String> values = next.getValue();

                String val = stdMap.get(key);
                if (!BinaryUtils.isEmpty(val) && val.contains(MULT_SYMBOL)) {
                    boolean flag = false;
                    String[] vals = new String[0];
                    try {
                        vals = val.split(MULT_SYMBOL);
                    } catch (Exception e) {
                        flag = true;
                        log.error("属性[" + key + "]引用值[" + val + "]数据格式异常，多选为数组格式");
                    }
                    for (String value : vals) {
                        if (!BinaryUtils.isEmpty(value) && !values.contains(value)) {
                            flag = true;
                            break;
                        }
                    }
                    if (flag) {
                        throw new IllegalArgumentException("属性[" + key + "]引用值[" + val + "]为多选格式，数据格式异常");
                    }
                } else {
                    Assert.isTrue(!isAdd || BinaryUtils.isEmpty(val) || values.contains(val), "属性[" + key + "]引用值[" + val + "]不存在");
                }
            }
        }
        String classCode = ciClass.getClassCode();
        // 校验重复
        List<String> ciPKAttrDefNames = CommUtil.getCiPKAttrDefNames(attrDefs);
        Integer hashCode = CommUtil.getCiMajorHashCodeExtra(attrs, ciPKAttrDefNames,classCode);
        List<String> ciPrimaryKeys = CommUtil.getCiPrimaryKeys(classCode, attrs, ciPKAttrDefNames);
        ciPrimaryKeys.add(0,classCode);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("domainId", ci.getDomainId()));
        boolQueryBuilder.must(QueryBuilders.termQuery("hashCode", hashCode));
        List<CcCiInfo> liveCis = esCiSvc.getCIInfoPageByQuery(1, 3000, boolQueryBuilder, false).getData();
        for (CcCiInfo cInfo : liveCis) {
            if (compareCiPrimaryKeys(ciPrimaryKeys, JSON.toList(cInfo.getCi().getCiPrimaryKey(), String.class))) {
                // 同分类下ciCode或id相同，更新，否则删除
                // 有id时优先按id更新，兼容仅有ciCode时更新的情况
                boolean idEqual = ci.getId() != null && ci.getId().longValue() == cInfo.getCi().getId().longValue();
                boolean codeEqual = ciCode != null && ciCode.equals(cInfo.getCi().getCiCode());
                boolean classIdEqual = ci.getClassId().longValue() == cInfo.getCi().getClassId().longValue();
                boolean isUpdate = (codeEqual || idEqual) && classIdEqual;
                if (isUpdate) {
                    if (ci.getId() != null && ciCode != null) {
                        Assert.isTrue(codeEqual && idEqual, "BS_CI_NO_EXIST");
                    }
                    ci.setId(cInfo.getCi().getId());
                    isAdd = false;
                } else {
                    throw MessageException.i18n("BS_MNAME_RECORD_CONTAINS");
                }
            }
        }

        if (isAdd && !BinaryUtils.isEmpty(ciCode)) {
            BoolQueryBuilder codeQuery = QueryBuilders.boolQuery();
            codeQuery.must(QueryBuilders.termQuery("ciCode.keyword", ciCode));
            codeQuery.must(boolQueryBuilder.must(QueryBuilders.termQuery("domainId", ci.getDomainId())));
            List<ESCIInfo> ciCodeQuery = esCiSvc.getListByQuery(codeQuery);
            if (!BinaryUtils.isEmpty(ciCodeQuery)) {
                ci.setId(ciCodeQuery.get(0).getId());
                isAdd = false;
            }
        }
        // 属性过滤，只保存定义过的属性
        Iterator<String> itAttr = stdMap.keySet().iterator();
        List<String> defNames = attrDefs.stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList());
        while (itAttr.hasNext()) {
            String attrName = itAttr.next();
            // 属性不包含在定义的属性，并且不是下划线开头结尾的字段，去掉
            if (!defNames.contains(attrName) && !attrName.matches("_([\\d\\D]*?)_")) {
                itAttr.remove();
            }
        }
        ciInfo.setAttrs(stdMap);
        Map<String, String> oldAttrs = null;
        // 组装CI
        if (isAdd) {
            long uuid = ESUtil.getUUID();
            ci.setId(uuid);
            ci.setCiCode(BinaryUtils.isEmpty(ciCode) ? String.valueOf(uuid) : ciCode);
            ci.setCreator(loginUser == null ? "system" : loginUser.getLoginCode());
            ci.setCreateTime(ESUtil.getNumberDateTime());
            ci.setCiVersion(String.valueOf(1));
            ci.setLocalVersion(0L);
            ci.setPublicVersion(1L);
        } else {
            CcCiInfo dbCIInfo = esCiSvc.getCiInfoById(ci.getId());
            Assert.notNull(dbCIInfo, "BS_CI_NO_EXIST");
            oldAttrs = dbCIInfo.getAttrs();
            // When the 3D model is opened, save the CI and add the path conversion
            this.removeModelIconPath(attrDefs, oldAttrs);
            ci.setCiCode(dbCIInfo.getCi().getCiCode());
            ci.setCreateTime(dbCIInfo.getCi().getCreateTime());
            ci.setCiVersion(dbCIInfo.getCi().getCiVersion());
            ci.setLocalVersion(0L);
            ci.setPublicVersion(dbCIInfo.getCi().getPublicVersion());
            if (!CheckAttrUtil.checkAttrMapEqual(stdMap, oldAttrs)) {
                ci.setCiVersion(String.valueOf(Long.parseLong(dbCIInfo.getCi().getCiVersion()) + 1L));
                ci.setPublicVersion(dbCIInfo.getCi().getPublicVersion() + 1L);
            }
        }
        ci.setClassId(classId);
        ci.setDataStatus(1);
        ci.setState(CIState.CREATE_COMPLETE.val());
        ci.setHashCode(hashCode);
        ci.setCiPrimaryKey(JSON.toString(ciPrimaryKeys));
        Long id = esCiSvc.saveOrUpdateCI(ciInfo);
        if (isAdd) {
            this.saveCIOperateLog(null, SysUtil.StaticUtil.LOG_DYNAMIC_INSERT, attrDefs, null, stdMap, ciClass.getClassName(), ci);
        } else {
            this.saveCIOperateLog(null, SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE, attrDefs, oldAttrs, stdMap, ciClass.getClassName(), ci);
        }
        return id;
    }

    @Override
    public ImportSheetMessage saveOrUpdateCiBath(Long domainId, CiClassSaveInfo saveInfo) {
        BinaryUtils.checkEmpty(saveInfo, "saveInfo");
        BinaryUtils.checkEmpty(saveInfo.getClassId(), "classId");
        SysUser loginUser = null;
        try {
            loginUser = SysUtil.getCurrentUserInfo();
        } catch (Exception e) {
            log.error("获取登陆用户失败，所有值取默认值");
        }
        saveInfo.setOwnerCode(BinaryUtils.isEmpty(saveInfo.getOwnerCode()) ? (loginUser == null ? "system" : loginUser.getLoginCode()) : saveInfo.getOwnerCode());
        ImportSheetMessage detailedResult = ImportSheetMessage.builder().build();
        List<ImportRowMessage> rowMessages = new ArrayList<>();
        // 统计信息
        int successCount = 0;
        int failCount = 0;
        int totals = 0;
        int ignores = 0;
        int inserts = 0;
        int updates = 0;
        List<String> sucessCIPKs = Lists.newArrayList();
        List<CcCiRecord> saveCiRecords = saveInfo.getRecords();
        // 保存CI
        if (!BinaryUtils.isEmpty(saveCiRecords)) {
            totals = saveCiRecords.size();
            Long classId = saveInfo.getClassId();
            Long sourceId = BinaryUtils.isEmpty(saveInfo.getSourceId()) ? 1L : saveInfo.getSourceId();
            // 获取当前及父级分类属性
            List<CcCiAttrDef> attrDefs = esClsSvc.getAllDefsByClassId(domainId, classId);
            List<String> defNames = attrDefs.stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList());
            ESCIClassInfo classInfo = esClsSvc.getById(classId);
            // 查询要更新的数据
            Map<String, CcCiInfo> ciCodeMap = new HashMap<>();
            Map<Long, String> idToCode = new HashMap<>();
            Set<String> ciCodeSet = saveCiRecords.stream().map(CcCiRecord::getCiCode).filter(code -> !BinaryUtils.isEmpty(code)).collect(Collectors.toSet());
            Set<Long> ciIds = saveCiRecords.stream().map(CcCiRecord::getCiId).filter(id -> !BinaryUtils.isEmpty(id)).collect(Collectors.toSet());
            if (!(BinaryUtils.isEmpty(ciCodeSet) && BinaryUtils.isEmpty(ciIds))) {
                BoolQueryBuilder query = QueryBuilders.boolQuery();
                if (!BinaryUtils.isEmpty(ciCodeSet)) {
                    query.should(QueryBuilders.termsQuery("ciCode.keyword", ciCodeSet));
                }
                if (!BinaryUtils.isEmpty(ciIds)) {
                    query.should(QueryBuilders.termsQuery("id", ciIds));
                }
                List<CcCiInfo> esInfos = esCiSvc.getCIInfoPageByQuery(1, saveCiRecords.size(), query, false).getData();
                if (!BinaryUtils.isEmpty(esInfos)) {
                    for (CcCiInfo esCiInfo : esInfos) {
                        ciCodeMap.put(esCiInfo.getCi().getCiCode(), esCiInfo);
                        idToCode.put(esCiInfo.getCi().getId(), esCiInfo.getCi().getCiCode());
                    }
                }
            }
            // 获取数据字典值
            Map<String, List<String>> dictValuesMap = this.getExterDictValues(domainId, attrDefs);

            List<CcCiInfo> ciInfosList = new ArrayList<CcCiInfo>();
            List<Integer> hashCodes = new ArrayList<>();
            Map<String, Integer> keyToIndex = new HashMap<>();
            List<String> saveCodes = new ArrayList<>();
            List<String> savePrimaryKeys = new ArrayList<>();
            Iterator<CcCiRecord> it = saveCiRecords.iterator();
            Map<String, ESCIOperateLog> updateLogMap = new HashMap<>();
            recordLoop:
            while (it.hasNext()) {
                boolean isUpdate = false;
                CcCiRecord record = it.next();
                Map<String, String> attrs = record.getAttrs();
                // 校验属性
                Map<String, Integer> errMsg = commSvc.validAttrs(attrDefs, attrs, true);
                if (!BinaryUtils.isEmpty(errMsg)) {
                    List<ImportCellMessage> cellMessages = new ArrayList<>();
                    errMsg.forEach((msg, errType) -> {
                        ImportCellMessage cellMessage = new ImportCellMessage();
                        cellMessage.setErrorType(errType);
                        cellMessage.setErrorDesc(msg);
                        cellMessages.add(cellMessage);
                    });
                    ImportRowMessage rowMessage = new ImportRowMessage();
                    rowMessage.setRowNum(record.getIndex());
                    rowMessage.setMessageItems(cellMessages);
                    rowMessages.add(rowMessage);
                    failCount++;
                    it.remove();
                    continue;
                }
                Map<String, String> stdMap = toStdMap(attrs);
                // 校验数据字典类型
                if (!BinaryUtils.isEmpty(dictValuesMap)) {
                    Iterator<Entry<String, List<String>>> dictIt = dictValuesMap.entrySet().iterator();
                    while (dictIt.hasNext()) {
                        Entry<String, List<String>> next = dictIt.next();
                        String key = next.getKey();
                        List<String> values = next.getValue();
                        String val = stdMap.get(key);
                        if (val.contains(MULT_SYMBOL)) {
                            if (!BinaryUtils.isEmpty(val)) {
                                boolean flag = false;
                                String[] vals = new String[0];
                                try {
                                    vals = val.split(MULT_SYMBOL);
                                } catch (Exception e) {
                                    flag = true;
                                    log.error("属性[" + key + "]引用值[" + val + "]数据格式异常，多选为数组格式");
                                }
                                for (String value : vals) {
                                    if (record.getCiId() == null && !BinaryUtils.isEmpty(value) && !values.contains(value)) {
                                        flag = true;
                                        break;
                                    }
                                }
                                if (flag) {
                                    rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "属性[" + key + "]引用值[" + val + "]不存在"));
                                    ignores++;
                                    it.remove();
                                    continue recordLoop;
                                }
                            }
                        } else {
                            if (record.getCiId() == null && !BinaryUtils.isEmpty(val) && !values.contains(val)) {
                                rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "属性[" + key + "]引用值[" + val + "]不存在"));
                                ignores++;
                                it.remove();
                                continue recordLoop;
                            }
                        }
                    }
                }
                String ciCode = record.getCiCode();
                // 兼容无ciCode时更新的情况
                if (BinaryUtils.isEmpty(ciCode) && record.getCiId() != null) {
                    ciCode = idToCode.get(record.getCiId());
                }
                // 校验ciCode，同一分类下，ciCode相同更新
                if (saveCodes.contains(ciCode)) {
                    ImportRowMessage importRowMessage = this.buildRowMessage(record.getIndex(), CheckAttrUtil.EXIST, "ciCode【" + ciCode + "】重复");
                    importRowMessage.setAttrs(attrs);
                    rowMessages.add(importRowMessage);
                    ignores++;
                    it.remove();
                    continue;
                } else if (ciCodeMap.containsKey(ciCode)) {
                    CcCiInfo ciInfo = ciCodeMap.get(ciCode);
                    if (ciInfo.getCi().getClassId().longValue() == classId.longValue()) {
                        record.setCiId(ciInfo.getCi().getId());
                        updates++;
                        isUpdate = true;
                    } else {
                        ImportRowMessage importRowMessage = this.buildRowMessage(record.getIndex(), CheckAttrUtil.EXIST, "ciCode【" + ciCode + "】重复");
                        importRowMessage.setAttrs(attrs);
                        rowMessages.add(importRowMessage);
                        ignores++;
                        it.remove();
                        continue;
                    }
                }
                if (!BinaryUtils.isEmpty(ciCode)) {
                    saveCodes.add(ciCode);
                }
                List<String> ciPKAttrDefNames = CommUtil.getCiPKAttrDefNames(attrDefs);
                // 获取所属分类下的业务主键值的hashCode
                Integer hashCode;
                try {
                    hashCode = CommUtil.getCiMajorHashCode(attrs, ciPKAttrDefNames, classInfo.getClassStdCode());
                } catch (Exception e) {
                    failCount++;
                    ImportRowMessage importRowMessage = this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "业务主键生成失败");
                    importRowMessage.setAttrs(attrs);
                    rowMessages.add(importRowMessage);
                    continue;
                }
                // 获取当前CI的业务主键值
                List<String> ciPrimaryKeys = CommUtil.getCiPrimaryKeys(classInfo.getClassStdCode(), attrs, ciPKAttrDefNames);
                String primaryKey = JSON.toString(ciPrimaryKeys);
                if (savePrimaryKeys.contains(primaryKey)) {
                    ignores++;
                    ImportRowMessage importRowMessage = this.buildRowMessage(record.getIndex(), CheckAttrUtil.EXIST, "业务主键【" + ciPrimaryKeys + "】重复");
                    importRowMessage.setAttrs(attrs);
                    rowMessages.add(importRowMessage);
                    continue;
                } else {
                    savePrimaryKeys.add(primaryKey);
                }
                CcCiInfo ciInfo = new CcCiInfo();
                // 属性过滤，只保存定义过的属性
                Iterator<String> itAttr = stdMap.keySet().iterator();
                while (itAttr.hasNext()) {
                    String attrName = itAttr.next();
                    // 属性不包含在定义的属性，并且不是下划线开头结尾的字段，去掉
                    if (!defNames.contains(attrName) && !attrName.matches("_([\\d\\D]*?)_")) {
                        itAttr.remove();
                    }
                }
                // CI 属性大小写转换
                ciInfo.setAttrs(stdMap);
                CcCi ci = new CcCi();
                if (isUpdate) {
                    CcCiInfo oldCi = ciCodeMap.get(ciCode);
                    ci.setCreateTime(oldCi.getCi().getCreateTime());
                    ci.setCiVersion(oldCi.getCi().getCiVersion());
                    // 属性合并
                    Map<String, String> combineAttrs = new HashMap<>();
                    combineAttrs.putAll(oldCi.getAttrs());
                    combineAttrs.putAll(stdMap);
                    ciInfo.setAttrs(combineAttrs);
                    ci.setLocalVersion(0L);
                    ci.setPublicVersion(oldCi.getCi().getPublicVersion());
                    if (!CheckAttrUtil.checkAttrMapEqual(combineAttrs, oldCi.getAttrs())) {
                        ci.setCiVersion(String.valueOf(Long.parseLong(oldCi.getCi().getCiVersion()) + 1L));
                        ci.setPublicVersion(oldCi.getCi().getPublicVersion() + 1L);
                    }
                } else {
                    ci.setCreator(loginUser == null ? "system" : loginUser.getLoginCode());
                    ci.setCreateTime(ESUtil.getNumberDateTime());
                    ci.setLocalVersion(0L);
                    ci.setPublicVersion(1L);
                }
                ci.setId(record.getCiId() == null ? ESUtil.getUUID() : record.getCiId());
                ci.setCiCode(ciCode);
                ci.setClassId(classId);
                ci.setSourceId(sourceId);
                ci.setDataStatus(1);
                ci.setHashCode(hashCode);
                ci.setCiPrimaryKey(primaryKey);
                ci.setCreateTime(ESUtil.getNumberDateTime());
                ci.setDomainId(domainId == null ? 1L : domainId);
                ciInfo.setCi(ci);
                ciInfosList.add(ciInfo);
                hashCodes.add(hashCode);
                keyToIndex.put(ci.getCiPrimaryKey(), record.getIndex());
                if (isUpdate) {
                    ESCIOperateLog ciLog = buildLogRecord(sourceId, SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE, attrDefs, ciCodeMap.get(ciCode).getAttrs(), stdMap, classInfo.getClassName(), ci);
                    updateLogMap.put(ciCode, ciLog);
                }
            }
            List<String> repeatKeys = new ArrayList<>();
            // 校验hashCode是否重复
            Map<String, Integer> res = this.checkRecordsByHashCode(ciInfosList, repeatKeys, updateLogMap);
            ignores += res.get("ignore") == null ? 0 : res.get("ignore");
            updates += res.get("update") == null ? 0 : res.get("update");
            if (!BinaryUtils.isEmpty(repeatKeys)) {
                for (String str : repeatKeys) {
                    rowMessages.add(this.buildRowMessage(keyToIndex.get(str), CheckAttrUtil.EXIST, "业务主键【" + str + "】重复"));
                }
            }
            // 保存CI操作日志
            List<ESCIOperateLog> ciLogs = new ArrayList<ESCIOperateLog>(updateLogMap.values());
            for (ESCIOperateLog ciLog : ciLogs) {
                ciLog.setSourceId(sourceId);
                ciLog.setCiClassName(classInfo.getClassName());
                ciLog.setProNames(attrDefs.stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList()));
                ciLog.setOperator(loginUser == null ? "system" : loginUser.getLoginCode());
            }
            Set<String> keySet = updateLogMap.keySet();
            if (ciInfosList.size() > keySet.size()) {
                ciInfosList.forEach(ciInfo -> {
                    if (BinaryUtils.isEmpty(ciInfo.getCi().getCiCode())) {
                        ciInfo.getCi().setCiCode(String.valueOf(ciInfo.getCi().getId()));
                    }
                    String ciCode = ciInfo.getCi().getCiCode();
                    if (!keySet.contains(ciCode)) {
                        ciLogs.add(buildLogRecord(sourceId, SysUtil.StaticUtil.LOG_DYNAMIC_INSERT, attrDefs, null, ciInfo.getAttrs(), classInfo.getClassName(), ciInfo.getCi()));
                    }
                });
            }
            Map<String, Object> saveOrUpdateMsg = esCiSvc.saveOrUpdateCIBatch(ciInfosList, false);
            ciInfosList.forEach(ci -> {
                sucessCIPKs.add(ci.getCi().getCiPrimaryKey());
            });
            if (!BinaryUtils.isEmpty(saveOrUpdateMsg.get("failCount"))) {
                failCount += (Integer) saveOrUpdateMsg.get("failCount");
            }
            inserts = (totals - failCount - updates - ignores);
            successCount = inserts + updates;
            if (!BinaryUtils.isEmpty(saveOrUpdateMsg.get("errMessge"))) {
                String errStr = JSON.toString(saveOrUpdateMsg.get("errMessge"));
                // 入库失败
                rowMessages.add(this.buildRowMessage(1, CheckAttrUtil.FAILURE, "入库失败：" + errStr));
            }
            this.saveCIOperateLogBatch(ciLogs);
        }
        // 汇总整理结果
        detailedResult.setSuccessNum(successCount);
        detailedResult.setFailNum(failCount);
        detailedResult.setInsertNum(inserts);
        detailedResult.setUpdateNum(updates);
        detailedResult.setIgnoreNum(ignores);
        detailedResult.setTotalNum(totals);
        detailedResult.setSucessCIPks(sucessCIPKs);
        if (!BinaryUtils.isEmpty(rowMessages)) {
            Collections.sort(rowMessages, FileUtil.ExcelUtil.getRowMessageComparator(rowMessages));
            detailedResult.setRowMessages(rowMessages);
        }
        return detailedResult;
    }

    @Override
    public CcCiInfo getCiInfoById(Long id) {
        return esCiSvc.getCiInfoById(id);
    }

    @Override
    public CcCiInfo getCiInfoByCiCode(String ciCode, String ownerCode) {
        return esCiSvc.getCiInfoByCiCode(ciCode,ownerCode);
    }

    @Override
    public CiGroupPage queryPageByIndex(Long domainId, Integer pageNum, Integer pageSize, CiQueryCdt cdt, Boolean hasClass) {
        return esCiSvc.queryPageByIndex(pageNum, pageSize, (CiQueryCdtExtend) cdt, hasClass);
    }

    @Override
    public CiGroupPage queryPageBySearchBean(@CIViewPermission ESCISearchBean bean, Boolean hasClass) {
        CiGroupPage ciPage = esCiSvc.queryPageBySearchBean(bean, hasClass);
        this.addModelIconPath(bean, ciPage);
        String[] ownerCodes = ciPage.getData().stream().map(e -> e.getCi().getOwnerCode()).distinct().toArray(String[]::new);
        Map<String, String> userMap = new HashMap<>();
        if (ownerCodes.length > 0) {
            CSysUser user = new CSysUser();
            user.setLoginCodes(ownerCodes);
            List<SysUser> sysUserByCdt = userSvc.getSysUserByCdt(user);
            userMap = sysUserByCdt.stream().collect(Collectors.toMap(SysUser::getLoginCode, SysUser::getUserName));
        }
        for (CcCiInfo info : ciPage.getData()) {
            String userName = userMap.getOrDefault(info.getCi().getOwnerCode(), "admin");
            info.getAttrs().put("所属用户", userName);
        }
        return ciPage;
    }


    @Override
    public List<CcCi> queryCiList(Long domainId, CCcCi cdt, String orders, Boolean isAsc) {
        List<CcCi> ciList = new ArrayList<>();
        if (cdt == null) {
            cdt = new CCcCi();
        }
        cdt.setDomainId(domainId);
        orders = BinaryUtils.isEmpty(orders) ? "modifyTime" : orders;
        List<CcCiInfo> ciInfos = esCiSvc.queryCiInfoList(cdt, orders, isAsc, false);
        for (CcCiInfo ciInfo : ciInfos) {
            ciList.add(ciInfo.getCi());
        }
        return ciList;
    }

    @Override
    public List<ESCIInfo> queryESCIInfoList(Long domainId, CCcCi cdt, String orders, Boolean isAsc) {
        if (cdt == null) {
            cdt = new CCcCi();
        }
        cdt.setDomainId(domainId);
        orders = BinaryUtils.isEmpty(orders) ? "modifyTime" : orders;
        return esCiSvc.queryESCiInfoList(cdt, orders, isAsc);
    }

    @Override
    public List<CcCiInfo> queryCiInfoList(Long domainId, CCcCi cdt, String orders, Boolean isAsc, Boolean hasClass) {
        if (cdt == null) {
            cdt = new CCcCi();
        }
        cdt.setDomainId(domainId);
        orders = BinaryUtils.isEmpty(orders) ? "modifyTime" : orders;
        return esCiSvc.queryCiInfoList(cdt, orders, isAsc, hasClass);
    }

    @Override
    public List<ESCIInfo> getESCIInfoListByCIPrimaryKeys(Long domainId, List<List<String>> ciPrimaryKeys) {
        if (BinaryUtils.isEmpty(ciPrimaryKeys)) {
            return Collections.emptyList();
        }
        return esCiSvc.getCIInfoListByCIPrimaryKeys(ciPrimaryKeys);
    }

    @Override
    public List<CcCiInfo> getCIInfoListByCIPrimaryKeys(Long domainId, List<List<String>> ciPrimaryKeys) {
        List<ESCIInfo> esciInfos = this.getESCIInfoListByCIPrimaryKeys(domainId, ciPrimaryKeys);
        return commSvc.transEsInfoList(esciInfos, false);
    }

    @Override
    public Page<CcCiInfo> queryCiInfoPage(Long domainId, Integer pageNum, Integer pageSize, CCcCi cdt, String orders, Boolean isAsc, Boolean hasClass) {
        if (cdt == null) {
            cdt = new CCcCi();
        }
        cdt.setDomainId(domainId);
        return esCiSvc.queryCiInfoPage(pageNum, pageSize, cdt, orders, isAsc, hasClass);
    }

    @Override
    public CcCiSearchPage searchCIByCdt(int pageNum, int pageSize, CCcCi bean) {
        if (BinaryUtils.isEmpty(bean)) {
            bean = new CCcCi();
        }
        return esCiSvc.searchCIByCdt(pageNum, pageSize, bean);
    }

    @Override
    public Page<ESCIInfo> searchESCIByBean(@CIViewPermission ESCISearchBean bean) {
        return esCiSvc.searchESCIByBean(bean);
    }

    @Override
    public CcCiSearchPage searchCIByBean(@CIViewPermission ESCISearchBean bean) {
        if (BinaryUtils.isEmpty(bean)) {
            bean = new ESCISearchBean();
            bean.setPageNum(1);
            bean.setPageSize(30);
        }
        return esCiSvc.searchCIByBean(bean);
    }

    @Override
    public Integer updateESCIInfoBatch(List<ESCIInfo> esCiInfoList) {
        return esCiSvc.updateESCIInfoBatch(esCiInfoList);
    }

    @Override
    public ImportResultMessage importCiByCiClsIds(MultipartFile file, Long classId) {
        // 校验分类
        Assert.notNull(classId, "X_PARAM_NOT_NULL${name:classId}");
        ESCIClassInfo classInfo = esClsSvc.getById(classId);
        Assert.notNull(classInfo, "BS_MNAME_CLASS_NOT_EXSIT");
        String className = classInfo.getClassName();
        ImportExcelMessage imResult = this.importCiExcel(file);
        String sheetName = null;
        for (String imSheetName : imResult.getSheetNames()) {
            if (className.equals(imSheetName)) {
                // sheet未填写领域的情况
                sheetName = imSheetName;
            }
        }
        if (sheetName == null) {
            throw MessageException.i18n("BS_MNAME_CLASS_DATA_ERROR", "{\"field\":" + className + "}");
        }
        CiExcelInfoDto infoDto = CiExcelInfoDto.builder().fileName(imResult.getFileName()).dirId(classInfo.getDirId()).sheetNames(Collections.singletonList(sheetName)).build();
        return this.importCiByClassBatch(classInfo.getDomainId(), infoDto, false);
    }

    @Override
    public ResponseEntity<byte[]> exportCiOrClass(ExportCiDto exportDto) {
        ValidDtoUtil.valid(exportDto);
        ResponseEntity<byte[]> res;
        if (exportDto.isDataMode()) {
            // 处理准确数据模式前置动作
            Set<Long> ciIds = exportDto.getCiIds();
            Page<ESCIInfo> cis;
            if(exportDto.getUsage() == 1){
                cis = esCiSvc.getListByQuery(1, ciIds.size(), QueryBuilders.termsQuery("id", ciIds));
            }else{
                cis = iamsCIDesignNonComplianceDao.getListByQuery(1, ciIds.size(), QueryBuilders.termsQuery("id", ciIds));
            }
            Assert.isTrue(cis.getData().size() == ciIds.size(), "指定的ciIds[" + com.alibaba.fastjson.JSON.toJSONString(ciIds) + "]有不存在数据");
            Set<Long> clsIds = cis.getData().stream().map(CcCi::getClassId).collect(Collectors.toSet());
            exportDto.setCiClassIds(clsIds);
        }
        Set<Long> classIds = exportDto.getCiClassIds();
        // 默认不导出类定义
        Integer hasClsDef = exportDto.getHasClsDef();
        // 默认只导出分类
        Integer hasData = exportDto.getHasData();
        // 默认导出全部类定义
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (!BinaryUtils.isEmpty(classIds)) {
            query.must(QueryBuilders.termsQuery("id", classIds));
        }
        List<ESCIClassInfo> ciClassInfos = esClsSvc.getListByQuery(query);
        //是否已选定所属部门进行导出模版
        for (ESCIClassInfo ciClassInfo : ciClassInfos) {
            if (ciClassInfo.getClassName().equals("岗位")&&exportDto.getDeptId()!=null){
                for (ESCIAttrDefInfo attrDef : ciClassInfo.getAttrDefs()) {
                    if (attrDef.getProStdName().equals("所属部门")){
                        attrDef.setIsRequired(0);
                        ESCIInfo byId = esCiSvc.getById(exportDto.getDeptId());
                        if (byId!=null){
                            attrDef.setDefVal(byId.getAttrs().get("部门主键").toString());
                        }
                    }
                }
            }
        }
        // 导出类定义
        String fileName = FileUtil.ExcelUtil.getExportFileName(new CIExportLabel().getCIFileName(true), CommUtil.EXCEL07_XLSX_EXTENSION, false);
        File export = null;
        Workbook swb = null;
        try {
            String path = URLDecoder.decode(ResourceUtils.getURL("classpath:").getPath(), "utf-8");
            File exportDir = new File(path + "/static/download");
            if (!exportDir.exists()) {
                exportDir.mkdirs();
            }
            export = new File(exportDir, "CI-" + CommUtil.EXPORT_TIME_FORMAT.format(new Date()));
            export.mkdir();
            InputStream is = this.getClass().getResourceAsStream("/static_res/ci_new_data_template.xlsx");
            swb = new SXSSFWorkbook(new XSSFWorkbook(is));
        } catch (Exception e) {
            log.error("生成excel失败：",e);
            throw new BinaryException("ci导出报错");
        }
        // 导出类定义
        if (hasClsDef == 1) {
            clsSvc.exportCIClassDef(swb, ciClassInfos);
        }
        // 导出类数据，只导出勾选的分类
        boolean dataNull = true;
        int dataCount = 0;
        int excelCount = 0;
        if (!(BinaryUtils.isEmpty(classIds) || BinaryUtils.isEmpty(ciClassInfos))) {
            // 标记CI数据是否为空，只要查到数据则设为false
            // 导出数据
            Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
            fileName = FileUtil.ExcelUtil.getExportFileName(new CIExportLabel().getCIFileName(false), CommUtil.EXCEL07_XLSX_EXTENSION, true);
            for (ESCIClassInfo classInfo : ciClassInfos) {
                List<CcCiAttrDef> attrDefs = esClsSvc.getAllDefsByClassId(domainId, classInfo.getId());
                if (BinaryUtils.isEmpty(attrDefs)) {
                    continue;
                }
                //是否已选定所属部门进行导出模版
                for (ESCIClassInfo ciClassInfo : ciClassInfos) {
                    if (ciClassInfo.getClassName().equals("岗位")&&exportDto.getDeptId()!=null){
                        for (CcCiAttrDef attrDef : attrDefs) {
                            if (attrDef.getProStdName().equals("所属部门")){
                                attrDef.setIsRequired(0);
                                ESCIInfo byId = esCiSvc.getById(exportDto.getDeptId());
                                if (byId!=null){
                                    attrDef.setDefVal(byId.getAttrs().get("部门主键").toString());
                                }
                            }
                        }
                    }
                }
                List<ESCIAttrDefInfo> attrEsDefs = classInfo.getAttrDefs();
                Map<String, ESCIAttrDefInfo> attrDefInfoMap = attrEsDefs.stream().collect(Collectors.toMap(e -> e.getProStdName(), e -> e, (k1, k2) -> k1));
                // 定义集合有序保存列头值
                List<String> titleCellValues = new ArrayList<String>();
                Set<String> reqCellValues = new HashSet<String>();
                // 获取业务主键属性定义
                Set<String> pkbCellValues = new HashSet<String>();
                // 指定ciCode列
                String majorCellValue = SysUtil.StaticUtil.CICODE_LABEL;
                // 默认第一列为ciCode列
                titleCellValues.add(majorCellValue);
                // 关联资产
                List<String> releAssetAttrNameList = new ArrayList<>();
                for (CcCiAttrDef attrDef : attrDefs) {
                    if (attrDef.getIsMajor() == 1) {
                        pkbCellValues.add(attrDef.getProName());
                        reqCellValues.add(attrDef.getProName());
                    } else if (attrDef.getIsRequired() == 1) {
                        reqCellValues.add(attrDef.getProName());
                    }
                    // 3D模型属性校验
                    int proType = attrDef.getProType();
                    if (!isShow3dAttribute && proType == AttrNameKeyEnum.MODEL.getType()) {
                        continue;
                    }
                    if (proType == AttrNameKeyEnum.LINK_CI.getType()) {
                        releAssetAttrNameList.add(attrDef.getProName());
                    }
                    titleCellValues.add(attrDef.getProName());

                }
                if (pkbCellValues.isEmpty()) {
                    continue;
                }
                String sheetName = CiExcelUtil.convertSheetNameSpecialChar(classInfo.getClassName());
                // 创建Sheet并设置标题行
                Sheet sheet = FileUtil.ExcelUtil.createExcelSheetAndTitle(swb, sheetName, titleCellValues, reqCellValues, pkbCellValues, majorCellValue);
                if (hasData == 1) {
                    // 兼容指定数据模式，只把指定的id填入到query中当筛选，简单处理沿用游标不做切换(照理说指定数据的就不应该这里再查了因为已经有了)
                    Map<String, Page<ESCIInfo>> rs = null;
                    BoolQueryBuilder exportQuery = QueryBuilders.boolQuery();
                    if (!CollectionUtils.isEmpty(exportDto.getWords())) {
                        exportQuery = buildExportQuery(exportDto);
                    }
                    if (exportDto.isDataMode()) {
                        exportQuery.must(QueryBuilders.termsQuery("id", exportDto.getCiIds()));
                    }
                    if (StringUtils.isNotBlank(exportDto.getGteTime())) {
                        Long gteTime = convertTime(exportDto.getGteTime());
                        exportQuery.must(QueryBuilders.rangeQuery("createTime").gte(gteTime));
                    }

                    if (StringUtils.isNotBlank(exportDto.getLteTime())) {
                        Long gteTime = convertEndTime(exportDto.getLteTime());
                        exportQuery.must(QueryBuilders.rangeQuery("createTime").lte(gteTime));
                    }
                    if (StringUtils.isNotBlank(exportDto.getOwnerCode())) {
                        exportQuery.must(QueryBuilders.termQuery("ownerCode.keyword",exportDto.getOwnerCode()));
                    }
                    exportQuery.must(QueryBuilders.termQuery("classId", classInfo.getId()));
                    if(exportDto.getUsage() == 1){
                        rs = esCiSvc.getScrollByQuery(1, 2000, exportQuery, "id", false);
                    }else{
                        rs = iamsCIDesignNonComplianceDao.getScrollByQuery(1, 2000, exportQuery, "id", false);
                    }
                    if (rs != null) {
                        String scrollId = rs.keySet().iterator().next();
                        Page<ESCIInfo> page = rs.get(scrollId);
                        long ciCount = 0;
                        long total = page.getTotalRows();
                        int rowNum = 1;
                        List<ESCIInfo> ciInfos = new ArrayList<>(page.getData());
                        Set<String> releAssetCiCodeList = new HashSet<>();
                        while (ciCount < total) {
                            List<ESCIInfo> secList;
                            if(exportDto.getUsage() == 1){
                                secList = esCiSvc.getListByScroll(scrollId);
                            } else {
                                secList = iamsCIDesignNonComplianceDao.getListByScroll(scrollId);
                            }
                            AssetManageUtil.getRlerAssetCiCodeList(releAssetAttrNameList, page, releAssetCiCodeList);
                            ciInfos.addAll(secList);
                            Map<String, String> releAssetMap = new HashMap<>();
                            // 根据ciCode 查询关联资产信息
                            if (!CollectionUtils.isEmpty(releAssetCiCodeList)) {
                                ESCISearchBean bean = new ESCISearchBean();
                                bean.setPageSize(releAssetCiCodeList.size());
                                bean.setCiCodes(Lists.newArrayList(releAssetCiCodeList));
                                Page<ESCIInfo> esciInfoPage = esCiSvc.searchESCIByBean(bean);
                                for (ESCIInfo esciInfo : esciInfoPage.getData()) {
                                    List<String> primaryList = JSONObject.parseArray(esciInfo.getCiPrimaryKey(), String.class);
                                    primaryList.remove(0);
                                    String primaryName = primaryList.stream().collect(Collectors.joining("|"));
                                    releAssetMap.put(esciInfo.getCiCode(), primaryName);
                                }
                            }
                            //不count了，有效率问题
                            //long count = this.countByCondition(query);
                            dataNull = false;
                            // 提取正文值map
                            List<Map<String, String>> commentValues = new ArrayList<Map<String, String>>();
                            for (ESCIInfo info : ciInfos) {
                                Map<String, Object> attrs = info.getAttrs();
                                if (attrs == null || attrs.isEmpty()) {
                                    continue;
                                }
                                attrs.put(majorCellValue, info.getCiCode());

                                // 导出时, 3D模型属性路径移除
                                this.ciExport3DmodelAttrPathCheck(attrDefs, attrs);
                                //导出用户
                                AssetManageUtil.ciExportAttrTransition(attrDefInfoMap, attrs);
                                // 导出处理关联资产字段
                                AssetManageUtil.ciExportTranRelevancyAsset(attrDefs, attrs, releAssetMap);
                                AssetManageUtil.ciExportPrefixIntegerAsset(attrDefs,attrs);
                                Map<String, String> attrStr = com.alibaba.fastjson.JSON.parseObject(com.alibaba.fastjson.JSON.toJSONString(attrs), new TypeReference<Map<String, String>>() {
                                });
                                commentValues.add(attrStr);
                            }
                            // 持续写入正文
                            if (!commentValues.isEmpty()) {
                                FileUtil.ExcelUtil.writeExcelComment(swb, sheet, rowNum, titleCellValues, null, null, commentValues);
                            }
                            ciCount += ciInfos.size();
                            dataCount += ciInfos.size();
                            rowNum += ciInfos.size();
                            ciInfos.clear();
                            // 数据量达到了单个Excel最大量,且不是最后一页,则需要写完当前Excel再创建新的Excel来存储
                            if (dataCount >= CommUtil.EXCEL_MAX_DATA_COUNT) {
                                excelCount++;
                                try {
                                    String tempFileName = FileUtil.ExcelUtil.getExportFileName(new CIExportLabel().getCIFileName(false), CommUtil.EXCEL07_XLSX_EXTENSION, true);
                                    File output = new File(export, tempFileName);
                                    FileOutputStream fileOutputStream = new FileOutputStream(output);
                                    swb.write(fileOutputStream);
                                    swb.close();
                                    fileOutputStream.close();
                                } catch (Exception e) {
                                    throw BinaryUtils.transException(e, ServiceException.class);
                                }
                                dataCount = 0;
                                rowNum = 1;
                                swb = new SXSSFWorkbook();
                                sheet = FileUtil.ExcelUtil.createExcelSheetAndTitle(swb, sheetName, titleCellValues, reqCellValues, pkbCellValues, majorCellValue);
                            }
                        }
                        if (exportDto.getUsage() == 1) {
                            esCiSvc.clearScroll(scrollId);
                        }else{
                            iamsCIDesignNonComplianceDao.clearScroll(scrollId);
                        }
                    }
                }
            }
        }
        File firstFile = null;
        // 末尾数据保存为一个excel
        if (dataCount > 0 || hasData != 1 || dataNull) {
            excelCount++;
            if (excelCount == 1) {
                firstFile = new File(export, fileName);
            }
            try {
                File output = new File(export, fileName);
                FileOutputStream fileOutputStream = new FileOutputStream(output);
                swb.write(fileOutputStream);
                swb.close();
                fileOutputStream.close();
            } catch (Exception e) {
                throw BinaryUtils.transException(e, ServiceException.class);
            }
        }
        if (excelCount > 1) {
            Compression.compressZip(new File(export.getPath()), new File(export.getPath() + ".zip"));
            res = ExcelUtil.returnRes(new File(export.getPath() + ".zip"));
        } else {
            res = ExcelUtil.returnRes(firstFile);
        }
        return res;
    }

    @Override
    public ImportExcelMessage importCiExcel(MultipartFile file) {
        // 校验文件
        boolean isXlsx = FileUtil.ExcelUtil.validExcelImportFile(file);
        ImportExcelMessage message = new ImportExcelMessage();
        message.setOriginalFileName(file.getOriginalFilename());
        File excelFile;
        try {
            //上传
            String fileName = CommUtil.getExportFileName(SysUtil.StaticUtil.CI, isXlsx ? CommUtil.EXCEL07_XLSX_EXTENSION : CommUtil.EXCEL03_XLS_EXTENSION);
            String pathName = File.separator + LocalDate.now() + File.separator + fileName;
            excelFile = new File(localPath + pathName);
            FileUtil.writeFile(excelFile, file.getBytes());
            message.setFileName(pathName);
        } catch (Exception e) {
            throw new MessageException(e.getMessage());
        }
        List<String> sheetNames = EasyExcelUtil.getExcelAllSheetNames(excelFile);
        Assert.isTrue(sheetNames.contains(SysUtil.StaticUtil.CI_DEF), "文档内容格式不正确");
        sheetNames.remove(SysUtil.StaticUtil.README_SHEETNAME);
        sheetNames.remove(SysUtil.StaticUtil.CI_DEF);
        Assert.notEmpty(sheetNames, "数据为空，不可导入！");
        message.setSheetNames(sheetNames);
        FileUtil.ExcelUtil.setImportExcelData(message, excelFile);
        return message;
    }

    @Override
    public ImportResultMessage importCiByClassBatch(Long domainId, CiExcelInfoDto excelInfoDto, boolean addAttr) {
        // 校验必要参数
        Assert.notNull(excelInfoDto.getFileName(), "X_PARAM_NOT_NULL${name:fileName}");
        Assert.notNull(excelInfoDto.getSheetNames(), "X_PARAM_NOT_NULL${name:sheetNames}");
        String clsDefKey = LanguageResolver.trans("BS_CI_CLASS_EXPORT_CLASS_DEFINITION");
        if (clsDefKey == null) {
            clsDefKey = "对象定义";
        }
        List<ImportSheetMessage> sheetMessages = new ArrayList<>();
        SysUser loginUser = SysUtil.getCurrentUserInfo();

        // 用于存储异步响应结果
        // saveMessage = new ArrayList<>();
        FileInputStream is = null;
        HSSFWorkbook workbook = null;
        // List<Long> saveClsIds = new ArrayList<>();
        // 读取本地文件
        rsmUtils.downloadRsmAndUpdateLocalRsm(excelInfoDto.getFileName());
        File file = new File(localPath + excelInfoDto.getFileName());
        List<String> sheetNames = excelInfoDto.getSheetNames().stream().map(String::trim)/*.map(String::toUpperCase)*/.collect(Collectors.toList());
        // 判断excel文件版本，03和07版本读取方式不同
        String excelType = CommUtil.getImportExcelType(file.getName());
        boolean isXlsx = true;
        if (excelType.toUpperCase().equals(CommUtil.EXCEL07_XLSX_EXTENSION.toUpperCase())) {
            isXlsx = true;
        } else if (excelType.toUpperCase().equals(CommUtil.EXCEL03_XLS_EXTENSION.toUpperCase())) {
            isXlsx = false;
        } else {
            throw MessageException.i18n("BS_MNAME_NOT_SUPPORT_FILETYPE");
        }
        try {
            // 数据sheet集合
            List<String> fileSheetNames = new ArrayList<>();
            // 分类定义内容
            List<String[]> clsSheetVal = null;
            if (isXlsx) {
                fileSheetNames = EasyExcelUtil.getExcelAllSheetNames(file);
                if (fileSheetNames.contains(clsDefKey)) {
                    EasyExcelUtil.SheetData sheetData = EasyExcelUtil.readSheet(clsDefKey, null, file);
                    clsSheetVal = sheetData.getRows();
                }
            } else {
                is = new FileInputStream(file);
                workbook = new HSSFWorkbook(is);
                for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                    Sheet sheet = workbook.getSheetAt(i);
                    if (sheet.getSheetName().equalsIgnoreCase(clsDefKey)) {
                        clsSheetVal = FileUtil.ExcelUtil.getSheetDatasArraybyPage(1, sheet.getLastRowNum(), sheet, null);
                    }
                    fileSheetNames.add(sheet.getSheetName());
                }
            }

            //原有数据权限中不包含admin的分类权限
            Map<String, SysRoleDataModuleRlt> oldNoAdminRoleDataModuleRltMap = new HashMap<>();
            if (excelInfoDto.isOverwriteData()) {
                //清空数据
                BoolQueryBuilder ciClassQuery = QueryBuilders.boolQuery();
                ciClassQuery.must(QueryBuilders.termQuery("domainId", domainId));
                ciClassQuery.must(QueryBuilders.termsQuery("className.keyword", sheetNames));
                List<ESCIClassInfo> ciClassInfos = esClsSvc.getListByQuery(ciClassQuery);
                if (!ciClassInfos.isEmpty()) {
                    List<Long> existClassIdList = new ArrayList<>();
                    Map<Long, ESCIClassInfo> oldClassMap = new HashMap<>();
                    ciClassInfos.forEach(esciClassInfo -> {
                        existClassIdList.add(esciClassInfo.getId());
                        oldClassMap.put(esciClassInfo.getId(), esciClassInfo);
                    });
                    //获取子分类
                    List<ESCIClassInfo> childs = esClsSvc.getListByQuery(QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("parentId", existClassIdList)));
                    childs.forEach(esciClassInfo -> {
                        existClassIdList.add(esciClassInfo.getId());
                        oldClassMap.put(esciClassInfo.getId(), esciClassInfo);
                    });
                    attrTransConfigSvc.deleteByQuery(QueryBuilders.termsQuery("classId", existClassIdList), true);
                    ciHistorySvc.deleteByQuery(QueryBuilders.termsQuery("classId", existClassIdList), true);
                    esClsSvc.deleteByIds(existClassIdList);
                    esCiSvc.deleteByQuery(QueryBuilders.termsQuery("classId",existClassIdList),true);
                    ciRltSvc.delRlts(null, existClassIdList, existClassIdList);

                    //删除分类授权
                    CSysRoleDataModuleRlt cdt = new CSysRoleDataModuleRlt();
                    String[] dataValues = new String[existClassIdList.size()];
                    for (int i = 0; i < existClassIdList.size(); i++) {
                        dataValues[i] = existClassIdList.get(i) + "";
                    }
                    cdt.setDataValues(dataValues);
                    cdt.setDomainId(domainId);
                    List<SysRoleDataModuleRlt> oldRoleDataModuleRlts = roleSvc.getRoleDataModuleRltByCdt(cdt);

                    //获取admin角色
                    BoolQueryBuilder roleQuery = QueryBuilders.boolQuery();
                    roleQuery.must(QueryBuilders.termQuery("domainId", domainId));
                    roleQuery.must(QueryBuilders.termQuery("roleName.keyword", "admin"));
                    List<SysRole> admin = roleSvc.getRolesByQuery(roleQuery);
                    if (admin.isEmpty()) {
                        throw new MessageException("缺少admin角色");
                    }
                    Long adminRoleId = admin.get(0).getId();
                    for (SysRoleDataModuleRlt oldRoleDataModuleRlt : oldRoleDataModuleRlts) {
                        if (!oldRoleDataModuleRlt.getRoleId().equals(adminRoleId)) {
                            Long oldClassId = Long.parseLong(oldRoleDataModuleRlt.getDataValue());
                            ESCIClassInfo esciClassInfo = oldClassMap.get(oldClassId);
                            oldNoAdminRoleDataModuleRltMap.put(esciClassInfo.getClassName(), oldRoleDataModuleRlt);
                        }
                    }
                    roleSvc.deleteRoleDataModuleRlt(cdt);
                }
            }

            // 先保存分类
            if (!BinaryUtils.isEmpty(clsSheetVal)) {
                ImportSheetMessage clsSheetMessage = clsSvc.importDirAndCIClass(domainId, clsSheetVal, sheetNames,addAttr);
                clsSheetMessage.setSheetName(clsDefKey);
                sheetMessages.add(clsSheetMessage);
            }
            // 查询分类数据
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termsQuery("className.keyword", sheetNames));
            boolQuery.must(QueryBuilders.termQuery("domainId", domainId));
            List<ESCIClassInfo> ciClassInfos = esClsSvc.getListByQuery(boolQuery);
            // 查询分类数据
            Map<String, ESCIClassInfo> clsMap = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
            clsMap.putAll(BinaryUtils.toObjectMap(ciClassInfos, "className"));
            // 循环保存数据
            for (String sheetName : fileSheetNames) {
                if (sheetNames.contains(sheetName)) {
                    List<String> titles = new ArrayList<>();
                    List<String[]> sheetVal = null;
                    if (isXlsx) {
                        EasyExcelUtil.SheetData sheetData = EasyExcelUtil.readSheet(sheetName, null, file);
                        // sheetVal =
                        // FileUtil.ExcelUtil.XLSXCovertCSVReader.readerExcelByFile(file,
                        // sheetName, 0);
                        titles = sheetData.getTitles();
                        sheetVal = sheetData.getRows();
                        if (BinaryUtils.isEmpty(sheetVal)) {
                            continue;
                        }
                        // Collections.addAll(titles, sheetVal.remove(0));
                    } else {
                        if (workbook == null) {
                            is = new FileInputStream(file);
                            workbook = new HSSFWorkbook(is);
                        }
                        HSSFSheet sheet = workbook.getSheet(sheetName);
                        titles.addAll(FileUtil.ExcelUtil.getSheetTitles(sheet).values());
                        sheetVal = FileUtil.ExcelUtil.getSheetDatasArraybyPage(1, sheet.getLastRowNum(), sheet, null);
                        if (sheetVal == null) {
                            continue;
                        }
                    }
                    ImportSheetMessage sheetMessage = ImportSheetMessage.builder().sheetName(sheetName).className(sheetName).build();
                    sheetMessages.add(sheetMessage);
                    if (titles == null || titles.size() <= 1) {
                        sheetMessage.setTotalNum(sheetVal.size());
                        sheetMessage.setFailNum(sheetVal.size());
                        sheetMessage.setErrMsg(LanguageResolver.trans("BS_MNAME_CLASS_DATA_ERROR", "{\"field\":" + sheetName + "}"));
                        continue;
                    }
                    Map<String, List<String>> attrMark = FileUtil.ExcelUtil.getAttrMarkByTitles(titles);
                    titles = attrMark.get("titles");
                    // 填充读取的原始titles
                    sheetMessage.setTitles(titles);
                    if (!clsMap.containsKey(sheetName)) {
                        sheetMessage.setTotalNum(sheetVal.size());
                        sheetMessage.setFailNum(sheetVal.size());
                        sheetMessage.setErrMsg(LanguageResolver.trans("BS_MNAME_CLASS_NOT_EXSIT"));
                        continue;
                    }
                    ESCIClassInfo ciClassInfo = clsMap.get(sheetName);
                    // CcCiClassInfo ciClassInfo = ciClassInfos.get(0);
                    // saveClsIds.add(ciClassInfo.getId());
                    if (ciClassInfo.getParentId() != 0) {
                        List<CcCiAttrDef> allDefss = esClsSvc.getAllDefsByClassId(domainId, ciClassInfo.getId());
                        ciClassInfo.setCcAttrDefs(allDefss);
                    }
                    List<CcCiAttrDef> attrDefs = ciClassInfo.getCcAttrDefs();
                    // CI属性定义不存在！ 主要防止数据不正常现象
                    if (BinaryUtils.isEmpty(attrDefs)) {
                        sheetMessage.setTotalNum(sheetVal.size());
                        sheetMessage.setFailNum(sheetVal.size());
                        sheetMessage.setErrMsg(LanguageResolver.trans("BS_MNAME_NOT_CI_ATTR_DEF"));
                        continue;
                    }
                    Long classId = ciClassInfo.getId();
                    String classStdCode = ciClassInfo.getClassStdCode();
                    String className = ciClassInfo.getClassName();
                    int rIdx = 1; // 数据行号，用于标识错误信息
                    List<CcCiRecord> ciRecords = new ArrayList<CcCiRecord>();
                    for (String[] rows : sheetVal) {
                        // 取第一列的值
                        String oneCellValue = rows[0];
                        // 转成map
                        Map<String, String> map = new HashMap<String, String>();
                        for (int j = 1; j < titles.size(); j++) {
                            String val = rows[j] == null ? rows[j] : rows[j].trim();
                            map.put(titles.get(j), val);
                        }
                        rIdx++;
                        CcCiRecord record = new CcCiRecord();
                        record.setIndex(rIdx);
                        record.setAttrs(map);
                        // 有ciCode且值不为空则填充ciCode
                        if (!BinaryUtils.isEmpty(oneCellValue)) {
                            record.setCiCode(oneCellValue);
                        }
                        ciRecords.add(record);
                    }
                    ImportSheetMessage classDetailedResult = new ImportSheetMessage(className);
                    String ownerCode = BinaryUtils.isEmpty(excelInfoDto.getOwnerCode()) ? loginUser.getLoginCode() : excelInfoDto.getOwnerCode();
                    if (!ciRecords.isEmpty()) {
                        while (ciRecords.size() > 0) {
                            List<CcCiRecord> subList = ciRecords.subList(0, ciRecords.size() > 3000 ? 3000 : ciRecords.size());
                            CiClassSaveInfo saveInfo = new CiClassSaveInfo(classId, classStdCode, null, ownerCode, subList, null);
                            classDetailedResult = this.saveOrUpdateCiBatchForExcelImport(saveInfo, ciClassInfo,excelInfoDto);
                            // 累计当前分类的结果后对当前分类的详情重新赋值
                            sheetMessage.setTotalNum(classDetailedResult.getTotalNum() + sheetMessage.getTotalNum());
                            sheetMessage.setSuccessNum(classDetailedResult.getSuccessNum() + sheetMessage.getSuccessNum());
                            sheetMessage.setFailNum(classDetailedResult.getFailNum() + sheetMessage.getFailNum());
                            sheetMessage.setIgnoreNum(classDetailedResult.getIgnoreNum() + sheetMessage.getIgnoreNum());
                            sheetMessage.setInsertNum(classDetailedResult.getInsertNum() + sheetMessage.getInsertNum());
                            sheetMessage.setUpdateNum(classDetailedResult.getUpdateNum() + sheetMessage.getUpdateNum());
                            if (!BinaryUtils.isEmpty(classDetailedResult.getRowMessages())) {
                                sheetMessage.getRowMessages().addAll(classDetailedResult.getRowMessages());
                            }
                            ciRecords.removeAll(subList);
                        }
                    }
                    // 等待每个分类落盘完成，防止不同分类间业务主键重复
                }
            }
            if(!CollectionUtils.isEmpty(ciClassInfos)){
                List<Long> classIds = ciClassInfos.stream().map(CcCiClass::getId).collect(Collectors.toList());
                classApiSvc.clearCacheEncode(classIds);
            }
        } catch (IllegalArgumentException e) {
            throw new MessageException(e.getMessage());
        } catch (MessageException e) {
            throw new MessageException(e.getMessage());
        } catch (NullPointerException e) {
            throw e;
        } catch (Exception e) {
            throw new MessageException(e.getMessage());
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                }
            }
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                }
            }
        }
        ImportResultMessage result = FileUtil.ExcelUtil.writeSheetMessageToFile(sheetMessages, "CI导入明细", clsDefKey);
        // 统计分类、CI导入详情
        for (ImportSheetMessage sheetMessage : sheetMessages) {
            if (clsDefKey.equalsIgnoreCase(sheetMessage.getSheetName())) {
                result.setDefTotalNum(sheetMessage.getTotalNum());
                result.setDefSuccessNum(sheetMessage.getSuccessNum());
                result.setDefFailNum(sheetMessage.getFailNum());
            }
        }
        // 增加名称判断[安全漏洞修复：对将要进行删除操作的文件名进行校验，禁止对系统敏感文件进行删除操作]
        if(file.getName().contains(excelInfoDto.getFileName())) {
            file.delete();
        }
        // 记录文件操作
        resourceSvc.saveSyncResourceInfo(excelInfoDto.getFileName(), urlPath + excelInfoDto.getFileName(), false, 1);
        // long time1 = System.currentTimeMillis();
        // long insertNum = 0;
        // for (ImportSheetMessage sheetMessage : sheetMessages) {
        // if (!clsDefKey.equals(sheetMessage.getSheetName())) {
        // insertNum += sheetMessage.getInsertNum();
        // }
        // }
        // long dbInsert = 0;
        // long errNum = 0;
        // while (insertNum - errNum > dbInsert) {
        // try {
        // Thread.sleep(2000);
        // } catch (InterruptedException e) {
        // }
        // dbInsert = 0;
        // Map<String, Long> clsCiCountMap =
        // esCiSvc.groupByCountField("classId", QueryBuilders.matchAllQuery());
        // for (Long clsId : saveClsIds) {
        // Long now = clsCiCountMap.get(String.valueOf(clsId)) == null ? 0L :
        // clsCiCountMap.get(String.valueOf(clsId));
        // Long old = dbCiCountMap.get(String.valueOf(clsId)) == null ? 0L :
        // dbCiCountMap.get(String.valueOf(clsId));
        // dbInsert += now - old;
        // }
        // errNum = 0;
        // for (Map<String, Object> saveMap : saveMessage) {
        // errNum += (Integer) saveMap.get("failCount") == null ? 0 : (Integer)
        // saveMap.get("failCount");
        // }
        // }
        // long time2 = System.currentTimeMillis();
        // log.info("wait for data save : " + (time2 - time1) + "ms");
        return result;
    }

    public ImportResultMessage importFlowSystemBatch(Long domainId, CiExcelInfoDto excelInfoDto) {
        // 校验必要参数
        Assert.notNull(excelInfoDto.getFileName(), "X_PARAM_NOT_NULL${name:fileName}");
        Assert.notNull(excelInfoDto.getSheetNames(), "X_PARAM_NOT_NULL${name:sheetNames}");

        String clsDefKey = LanguageResolver.trans("BS_CI_CLASS_EXPORT_CLASS_DEFINITION");
        if (clsDefKey == null) {
            clsDefKey = "对象定义";
        }
        List<ImportSheetMessage> sheetMessages = new ArrayList<>();
        // 用于存储异步响应结果
        FileInputStream is = null;
        HSSFWorkbook workbook = null;
        // 读取本地文件
        rsmUtils.downloadRsmAndUpdateLocalRsm(excelInfoDto.getFileName());
        File file = new File(localPath + excelInfoDto.getFileName());
        List<String> sheetNames = excelInfoDto.getSheetNames().stream().map(String::trim)/*.map(String::toUpperCase)*/.collect(Collectors.toList());
        // 判断excel文件版本，03和07版本读取方式不同
        String excelType = CommUtil.getImportExcelType(file.getName());
        boolean isXlsx;
        if (excelType.toUpperCase().equals(CommUtil.EXCEL07_XLSX_EXTENSION.toUpperCase())) {
            isXlsx = true;
        } else if (excelType.toUpperCase().equals(CommUtil.EXCEL03_XLS_EXTENSION.toUpperCase())) {
            isXlsx = false;
        } else {
            throw MessageException.i18n("BS_MNAME_NOT_SUPPORT_FILETYPE");
        }
        try {
            // 数据sheet集合
            List<String> fileSheetNames = new ArrayList<>();
            // 分类定义内容
            if (isXlsx) {
                fileSheetNames = EasyExcelUtil.getExcelAllSheetNames(file);
            } else {
                is = new FileInputStream(file);
                workbook = new HSSFWorkbook(is);
                for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                    Sheet sheet = workbook.getSheetAt(i);
                    fileSheetNames.add(sheet.getSheetName());
                }
            }
            ArrayList<String> flowNames = new ArrayList<>();
            flowNames.add("流程组");
            flowNames.add("流程");
            fileSheetNames.removeIf(s -> {
                if (!flowNames.contains(s)) {
                    return true;
                } else {
                    return false;
                }
            });

            //原有数据权限中不包含admin的分类权限
            Map<String, SysRoleDataModuleRlt> oldNoAdminRoleDataModuleRltMap = new HashMap<>();
            if (excelInfoDto.isOverwriteData()) {
                //清空数据
                BoolQueryBuilder ciClassQuery = QueryBuilders.boolQuery();
                ciClassQuery.must(QueryBuilders.termQuery("domainId", domainId));
                ciClassQuery.must(QueryBuilders.termsQuery("className.keyword", sheetNames));
                List<ESCIClassInfo> ciClassInfos = esClsSvc.getListByQuery(ciClassQuery);
                if (!ciClassInfos.isEmpty()) {
                    List<Long> existClassIdList = new ArrayList<>();
                    Map<Long, ESCIClassInfo> oldClassMap = new HashMap<>();
                    ciClassInfos.forEach(esciClassInfo -> {
                        existClassIdList.add(esciClassInfo.getId());
                        oldClassMap.put(esciClassInfo.getId(), esciClassInfo);
                    });
                    //获取子分类
                    List<ESCIClassInfo> childs = esClsSvc.getListByQuery(QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("parentId", existClassIdList)));
                    childs.forEach(esciClassInfo -> {
                        existClassIdList.add(esciClassInfo.getId());
                        oldClassMap.put(esciClassInfo.getId(), esciClassInfo);
                    });
                    attrTransConfigSvc.deleteByQuery(QueryBuilders.termsQuery("classId", existClassIdList), true);
                    ciHistorySvc.deleteByQuery(QueryBuilders.termsQuery("classId", existClassIdList), true);
                    esClsSvc.deleteByIds(existClassIdList);
                    esCiSvc.deleteByQuery(QueryBuilders.termsQuery("classId", existClassIdList), true);
                    ciRltSvc.delRlts(null, existClassIdList, existClassIdList);

                    //删除分类授权
                    CSysRoleDataModuleRlt cdt = new CSysRoleDataModuleRlt();
                    String[] dataValues = new String[existClassIdList.size()];
                    for (int i = 0; i < existClassIdList.size(); i++) {
                        dataValues[i] = existClassIdList.get(i) + "";
                    }
                    cdt.setDataValues(dataValues);
                    cdt.setDomainId(domainId);
                    List<SysRoleDataModuleRlt> oldRoleDataModuleRlts = roleSvc.getRoleDataModuleRltByCdt(cdt);

                    //获取admin角色
                    BoolQueryBuilder roleQuery = QueryBuilders.boolQuery();
                    roleQuery.must(QueryBuilders.termQuery("domainId", domainId));
                    roleQuery.must(QueryBuilders.termQuery("roleName.keyword", "admin"));
                    List<SysRole> admin = roleSvc.getRolesByQuery(roleQuery);
                    if (admin.isEmpty()) {
                        throw new MessageException("缺少admin角色");
                    }
                    Long adminRoleId = admin.get(0).getId();
                    for (SysRoleDataModuleRlt oldRoleDataModuleRlt : oldRoleDataModuleRlts) {
                        if (!oldRoleDataModuleRlt.getRoleId().equals(adminRoleId)) {
                            Long oldClassId = Long.parseLong(oldRoleDataModuleRlt.getDataValue());
                            ESCIClassInfo esciClassInfo = oldClassMap.get(oldClassId);
                            oldNoAdminRoleDataModuleRltMap.put(esciClassInfo.getClassName(), oldRoleDataModuleRlt);
                        }
                    }
                    roleSvc.deleteRoleDataModuleRlt(cdt);
                }
            }
            // 查询分类数据
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termsQuery("className.keyword", fileSheetNames));
            boolQuery.must(QueryBuilders.termQuery("domainId", domainId));
            List<ESCIClassInfo> ciClassInfos = esClsSvc.getListByQuery(boolQuery);
            // 查询分类数据
            Map<String, ESCIClassInfo> clsMap = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
            clsMap.putAll(BinaryUtils.toObjectMap(ciClassInfos, "className"));
            // 循环保存数据
            importFlowCiInfoBySheetName("流程组", isXlsx, file, workbook, is, sheetMessages, clsMap);
            importFlowCiInfoBySheetName("流程", isXlsx, file, workbook, is, sheetMessages, clsMap);
            if (!CollectionUtils.isEmpty(ciClassInfos)) {
                List<Long> classIds = ciClassInfos.stream().map(CcCiClass::getId).collect(Collectors.toList());
                classApiSvc.clearCacheEncode(classIds);
            }
        } catch (Exception e) {
            throw new MessageException(e.getMessage());
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                }
            }
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                }
            }
        }
        ImportResultMessage result = FileUtil.ExcelUtil.writeSheetMessageToFile(sheetMessages, "CI导入明细", clsDefKey);
        // 统计分类、CI导入详情
        for (ImportSheetMessage sheetMessage : sheetMessages) {
            if (clsDefKey.equalsIgnoreCase(sheetMessage.getSheetName())) {
                result.setDefTotalNum(sheetMessage.getTotalNum());
                result.setDefSuccessNum(sheetMessage.getSuccessNum());
                result.setDefFailNum(sheetMessage.getFailNum());
            }
        }
        // 增加名称判断[安全漏洞修复：对将要进行删除操作的文件名进行校验，禁止对系统敏感文件进行删除操作]
        if (file.getName().contains(excelInfoDto.getFileName())) {
            file.delete();
        }
        // 记录文件操作
        resourceSvc.saveSyncResourceInfo(excelInfoDto.getFileName(), urlPath + excelInfoDto.getFileName(), false, 1);
        return result;
    }

    private void importFlowCiInfoBySheetName(String sheetName,boolean isXlsx,File file,HSSFWorkbook workbook
            ,FileInputStream is,List<ImportSheetMessage> sheetMessages,Map<String, ESCIClassInfo> clsMap) throws IOException {
        List<String> titles = new ArrayList<>();
        List<String[]> sheetVal = null;
        if (isXlsx) {
            EasyExcelUtil.SheetData sheetData = EasyExcelUtil.readSheet(sheetName, null, file);
            titles = sheetData.getTitles();
            sheetVal = sheetData.getRows();
            if (BinaryUtils.isEmpty(sheetVal)) {
                return;
            }
        } else {
            if (workbook == null) {
                is = new FileInputStream(file);
                workbook = new HSSFWorkbook(is);
            }
            HSSFSheet sheet = workbook.getSheet(sheetName);
            titles.addAll(FileUtil.ExcelUtil.getSheetTitles(sheet).values());
            sheetVal = FileUtil.ExcelUtil.getSheetDatasArraybyPage(1, sheet.getLastRowNum(), sheet, null);
            if (sheetVal == null) {
                return;
            }
        }
        ImportSheetMessage sheetMessage = ImportSheetMessage.builder().sheetName(sheetName).className(sheetName).build();
        sheetMessages.add(sheetMessage);
        if (titles == null || titles.size() <= 1) {
            sheetMessage.setTotalNum(sheetVal.size());
            sheetMessage.setFailNum(sheetVal.size());
            sheetMessage.setErrMsg(LanguageResolver.trans("BS_MNAME_CLASS_DATA_ERROR", "{\"field\":" + sheetName + "}"));
            return;
        }
        Map<String, List<String>> attrMark = FileUtil.ExcelUtil.getAttrMarkByTitles(titles);
        titles = attrMark.get("titles");
        // 填充读取的原始titles
        sheetMessage.setTitles(titles);
        if (!clsMap.containsKey(sheetName)) {
            sheetMessage.setTotalNum(sheetVal.size());
            sheetMessage.setFailNum(sheetVal.size());
            sheetMessage.setErrMsg(LanguageResolver.trans("BS_MNAME_CLASS_NOT_EXSIT"));
            return;
        }
        ESCIClassInfo ciClassInfo = clsMap.get(sheetName);

        if (ciClassInfo.getParentId() != 0) {
            List<CcCiAttrDef> allDefss = esClsSvc.getAllDefsByClassId(1L, ciClassInfo.getId());
            ciClassInfo.setCcAttrDefs(allDefss);
        }
        List<CcCiAttrDef> attrDefs = ciClassInfo.getCcAttrDefs();
        // CI属性定义不存在！ 主要防止数据不正常现象
        if (BinaryUtils.isEmpty(attrDefs)) {
            sheetMessage.setTotalNum(sheetVal.size());
            sheetMessage.setFailNum(sheetVal.size());
            sheetMessage.setErrMsg(LanguageResolver.trans("BS_MNAME_NOT_CI_ATTR_DEF"));
            return;
        }
        Long classId = ciClassInfo.getId();
        String classStdCode = ciClassInfo.getClassStdCode();
        String className = ciClassInfo.getClassName();
        int rIdx = 1; // 数据行号，用于标识错误信息
        List<CcCiRecord> ciRecords = new ArrayList<CcCiRecord>();
        for (String[] rows : sheetVal) {
            // 取第一列的值
            String oneCellValue = rows[0];
            // 转成map
            Map<String, String> map = new HashMap<String, String>();
            for (int j = 1; j < titles.size(); j++) {
                String val = rows[j] == null ? rows[j] : rows[j].trim();
                map.put(titles.get(j), val);
            }
            rIdx++;
            CcCiRecord record = new CcCiRecord();
            record.setIndex(rIdx);
            record.setAttrs(map);
            // 有ciCode且值不为空则填充ciCode
            if (!BinaryUtils.isEmpty(oneCellValue)) {
                record.setCiCode(oneCellValue);
            }
            ciRecords.add(record);
        }
        //将流程组转层级，分层保存数据
        HashMap<String, List<CcCiRecord>> flowLevelCcCiRecordMap = new HashMap<>();
        ImportSheetMessage classDetailedResult = new ImportSheetMessage(className);
        String ownerCode = "admin";
        if ("流程组".equalsIgnoreCase(sheetName)) {
            ESDictionaryItemSearchBean esDictionaryItemSearchBean = new ESDictionaryItemSearchBean();
            esDictionaryItemSearchBean.setDictName("流程级别");
            Page<ESDictionaryItemInfo> esDictionaryItemInfoPage = dictSvc.searchDictItemPageByBean(esDictionaryItemSearchBean);
            if (CollectionUtils.isEmpty(esDictionaryItemInfoPage.getData())) {
                throw new RuntimeException("流程状态字典不存在");
            }
            Map<String, String> flowLevelMap = new HashMap<>();
            for (ESDictionaryItemInfo datum : esDictionaryItemInfoPage.getData()) {
                flowLevelMap.put(datum.getAttrs().get("流程级别ID"), datum.getAttrs().get("流程级别名称"));
            }
            List<ESDictionaryItemInfo> flowLevelList = esDictionaryItemInfoPage.getData();
            flowLevelList.sort(Comparator.comparing(o -> o.getAttrs().get("流程级别ID")));
            Collection<String> flowLevelNames = flowLevelMap.values();
            Iterator<CcCiRecord> iterator = ciRecords.iterator();
            //转层级
            while (iterator.hasNext()){
                CcCiRecord ciRecord = iterator.next();
                List<CcCiRecord> flowGroupLevel = flowLevelCcCiRecordMap.get(ciRecord.getAttrs().get("流程级别"));
                if (flowGroupLevel == null) {
                    flowGroupLevel = new ArrayList<>();
                }
                flowGroupLevel.add(ciRecord);
                String flowLevelName = ciRecord.getAttrs().get("流程级别");
                if(flowLevelNames.contains(flowLevelName)){
                    flowLevelCcCiRecordMap.put(flowLevelName, flowGroupLevel);
                }else {
                    iterator.remove();
                    sheetMessage.setTotalNum(1 + sheetMessage.getTotalNum());
                    sheetMessage.setFailNum(1 + sheetMessage.getFailNum());
                    sheetMessage.getRowMessages().add(this.buildRowMessage(ciRecord.getIndex(), CheckAttrUtil.FAILURE, "属性[流程级别]引用值[" + flowLevelName + "]不存在"));
                }
            }

            for (ESDictionaryItemInfo esDictionaryItemInfo : flowLevelList) {
                String s = esDictionaryItemInfo.getAttrs().get("流程级别名称");
                List<CcCiRecord> ccCiRecords = flowLevelCcCiRecordMap.get(s);
                if (!CollectionUtils.isEmpty(ccCiRecords)) {
                    while (!ccCiRecords.isEmpty()) {
                        List<CcCiRecord> subList = ccCiRecords.subList(0, Math.min(ccCiRecords.size(), 3000));
                        CiClassSaveInfo saveInfo = new CiClassSaveInfo(classId, classStdCode, null, ownerCode, subList, null);
                        classDetailedResult = this.saveOrUpdateFlowCiBatchForExcelImport(saveInfo, ciClassInfo, flowLevelMap);
                        // 累计当前分类的结果后对当前分类的详情重新赋值
                        sheetMessage.setTotalNum(classDetailedResult.getTotalNum() + sheetMessage.getTotalNum());
                        sheetMessage.setSuccessNum(classDetailedResult.getSuccessNum() + sheetMessage.getSuccessNum());
                        sheetMessage.setFailNum(classDetailedResult.getFailNum() + sheetMessage.getFailNum());
                        sheetMessage.setIgnoreNum(classDetailedResult.getIgnoreNum() + sheetMessage.getIgnoreNum());
                        sheetMessage.setInsertNum(classDetailedResult.getInsertNum() + sheetMessage.getInsertNum());
                        sheetMessage.setUpdateNum(classDetailedResult.getUpdateNum() + sheetMessage.getUpdateNum());
                        if (!BinaryUtils.isEmpty(classDetailedResult.getRowMessages())) {
                            sheetMessage.getRowMessages().addAll(classDetailedResult.getRowMessages());
                        }
                        ccCiRecords.removeAll(subList);
                    }
                }
            }
        } else if("流程".equalsIgnoreCase(sheetName)) {
            if (!CollectionUtils.isEmpty(ciRecords)) {
                while (!ciRecords.isEmpty()) {
                    List<CcCiRecord> subList = ciRecords.subList(0, Math.min(ciRecords.size(), 3000));
                    CiClassSaveInfo saveInfo = new CiClassSaveInfo(classId, classStdCode, null, ownerCode, subList, null);
                    classDetailedResult = this.saveOrUpdateFlowCiBatchForExcelImport(saveInfo, ciClassInfo, null);
                    // 累计当前分类的结果后对当前分类的详情重新赋值
                    sheetMessage.setTotalNum(classDetailedResult.getTotalNum() + sheetMessage.getTotalNum());
                    sheetMessage.setSuccessNum(classDetailedResult.getSuccessNum() + sheetMessage.getSuccessNum());
                    sheetMessage.setFailNum(classDetailedResult.getFailNum() + sheetMessage.getFailNum());
                    sheetMessage.setIgnoreNum(classDetailedResult.getIgnoreNum() + sheetMessage.getIgnoreNum());
                    sheetMessage.setInsertNum(classDetailedResult.getInsertNum() + sheetMessage.getInsertNum());
                    sheetMessage.setUpdateNum(classDetailedResult.getUpdateNum() + sheetMessage.getUpdateNum());
                    if (!BinaryUtils.isEmpty(classDetailedResult.getRowMessages())) {
                        sheetMessage.getRowMessages().addAll(classDetailedResult.getRowMessages());
                    }
                    ciRecords.removeAll(subList);
                }
            }
        }
    }

    @Override
    public Integer deleteById(Long id, Long sourceId) {
        ciRltSvc.delRltByCiId(id);
        ESCIInfo esciInfo = esCiSvc.getById(id);
        CcCiInfo ciInfo = commSvc.tranCcCiInfo(esciInfo, true);
        // CcCiInfo ciInfo = getCiInfoById(id);
        // 记录CI操作日志
        this.saveCIOperateLog(sourceId, SysUtil.StaticUtil.LOG_DYNAMIC_DELETE, ciInfo.getAttrDefs(), ciInfo.getAttrs(), null, ciInfo.getCiClass().getClassName(), ciInfo.getCi());
        // 记录CI历史版本
        esCiSvc.transCIAttrs(Collections.singletonList(esciInfo), false);
        esHistorySvc.saveOrUpdateHistoryInfo(esciInfo, ESCIHistoryInfo.ActionType.DELETE);
        //将对应的ciCode从文件夹中移除
        removeCiCodeFromFolder(Arrays.asList(ciInfo));
        return esCiSvc.deleteById(id);
    }

    @Override
    public Integer removeByIds(List<Long> ciIds, Long sourceId) {
        if (BinaryUtils.isEmpty(ciIds)) {
            return 1;
        }
        CCcCi cdt = new CCcCi();
        cdt.setIds(ciIds.toArray(new Long[]{}));
        // List<CcCiInfo> ciInfos = this.queryCiInfoPage(1L, 1, ciIds.size(),
        // cdt, null, true, true).getData();
        List<ESCIInfo> esciInfos = esCiSvc.getSortListByCdt(1, ciIds.size(), cdt, null, true).getData();
        List<CcCiInfo> ciInfos = commSvc.transEsInfoList(esciInfos, true);
        List<ESCIOperateLog> ciLogs = new ArrayList<>();
        ciInfos.forEach(ciInfo -> {
            ciLogs.add(buildLogRecord(sourceId, SysUtil.StaticUtil.LOG_DYNAMIC_DELETE, ciInfo.getAttrDefs(), ciInfo.getAttrs(), null, ciInfo.getCiClass().getClassName(), ciInfo.getCi()));
        });
        this.saveCIOperateLogBatch(ciLogs);
        esCiSvc.transCIAttrs(esciInfos, false);
        esHistorySvc.saveOrUpdateHistoryInfosBatch(esciInfos, ESCIHistoryInfo.ActionType.DELETE);
        ciRltSvc.delRltByCiIds(ciIds);
        try {
            iamsCIDesignNonComplianceDao.deleteByIds(ciIds);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //将对应的ciCode从文件夹中移除
        removeCiCodeFromFolder(ciInfos);
        return esCiSvc.deleteByIds(ciIds);
    }

    private void removeCiCodeFromFolder(List<CcCiInfo> ciInfos) {
        // 提取 ciCode 列表
        List<String> ciCodes = ciInfos.stream()
                .map(ciInfo -> ciInfo.getCi().getCiCode())
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(ciCodes)) {
            List<EamDirectoryObjectAssociation> list = eamDirectoryObjectAssociationDao.getListByQuery(QueryBuilders.termsQuery("ciCode.keyword", ciCodes));
            if (!CollectionUtils.isEmpty(list)) {
                eamDirectoryObjectAssociationDao.deleteByIds(list.stream().map(EamDirectoryObjectAssociation::getId).collect(Collectors.toList()));
            }
        }
        //删除关联表信息
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termsQuery("linkedCiCode.keyword", ciCodes));
        boolQueryBuilder.filter(QueryBuilders.termQuery("status", 1));
        List<FlowSystemAssociatedFeatures> listByQueryScroll = flowSystemAssociatedFeaturesDao.getListByQueryScroll(boolQueryBuilder);
        if (!CollectionUtils.isEmpty(listByQueryScroll)) {
            List<Long> ids = listByQueryScroll.stream().map(FlowSystemAssociatedFeatures::getId).collect(Collectors.toList());
            flowSystemAssociatedFeaturesDao.deleteByIds(ids);
        }
    }

    @Override
    public Integer removeByPrimaryKeys(Long domainId, List<String> ciPrimaryKeys, Long sourceId) {
        Assert.isTrue(!BinaryUtils.isEmpty(ciPrimaryKeys), "X_PARAM_NOT_NULL${name:ciPrimaryKeys}");
        ESCISearchBean bean = new ESCISearchBean();
        bean.setCiPrimaryKeys(ciPrimaryKeys);
        bean.setPageNum(1);
        bean.setPageSize(ciPrimaryKeys.size());
        Page<ESCIInfo> page = searchESCIByBean(bean);
        Page<CcCiInfo> ciInfos = commSvc.transEsInfoPage(page, true);
        List<ESCIOperateLog> ciLogs = new ArrayList<>();
        List<Long> ciIds = new ArrayList<>();
        ciInfos.getData().forEach(ciInfo -> {
            ciIds.add(ciInfo.getCi().getId());
            ciLogs.add(buildLogRecord(sourceId, SysUtil.StaticUtil.LOG_DYNAMIC_DELETE, ciInfo.getAttrDefs(), ciInfo.getAttrs(), null, ciInfo.getCiClass().getClassName(), ciInfo.getCi()));
        });
        this.saveCIOperateLogBatch(ciLogs);
        esCiSvc.transCIAttrs(page.getData(), false);
        esHistorySvc.saveOrUpdateHistoryInfosBatch(page.getData(), ESCIHistoryInfo.ActionType.DELETE);
        ciRltSvc.delRltByCiIds(ciIds);
        return esCiSvc.deleteByIds(ciIds);
    }

    @Override
    public Integer removeByClassId(Long classId, Long sourceId) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("classId", classId));
        long count = esCiSvc.countByCondition(query);
        if (count > 0) {
            long totalPages = count % 3000 == 0 ? count / 3000 : count / 3000 + 1;
            int pageNum = 0;
            while (pageNum < totalPages) {
                List<ESCIInfo> esciInfos = esCiSvc.getListByQuery(++pageNum, 3000, query).getData();
                List<CcCiInfo> ciInfos = commSvc.transEsInfoList(esciInfos, true);
                // List<CcCiInfo> ciInfos =
                // esCiSvc.getCIInfoPageByQuery(pageNum++, 3000, query,
                // true).getData();
                List<ESCIOperateLog> ciLogs = new ArrayList<>();
                ciInfos.forEach(ciInfo -> {
                    ciLogs.add(buildLogRecord(sourceId, SysUtil.StaticUtil.LOG_DYNAMIC_DELETE, ciInfo.getAttrDefs(), ciInfo.getAttrs(), null, ciInfo.getCiClass().getClassName(), ciInfo.getCi()));
                });
                this.saveCIOperateLogBatch(ciLogs);
                esCiSvc.transCIAttrs(esciInfos, false);
                esHistorySvc.saveOrUpdateHistoryInfosBatch(esciInfos, ESCIHistoryInfo.ActionType.DELETE);
            }
            ciRltSvc.delRltByCiClassId(classId);
            esCiSvc.removeByClassId(classId);
        }
        return 1;
    }

    /**
     * 专为一键导入提供的批量保存接口
     *
     * @param saveInfo    要保存的CI列表
     * @param ciClassInfo 分类信息，子分类需要携带父级属性
     * @return
     */
    private ImportSheetMessage saveOrUpdateCiBatchForExcelImport(CiClassSaveInfo saveInfo, ESCIClassInfo ciClassInfo, CiExcelInfoDto excelInfoDto) {
        BinaryUtils.checkEmpty(saveInfo, "saveInfo");
        BinaryUtils.checkEmpty(ciClassInfo, "classInfo");
        BinaryUtils.checkEmpty(ciClassInfo.getId(), "classId");
        BinaryUtils.checkEmpty(ciClassInfo.getCcAttrDefs(), "attrDefs");
        ImportSheetMessage detailedResult = ImportSheetMessage.builder().className(ciClassInfo.getClassName()).build();
        // 待保存数据为空，直接返回
        List<CcCiRecord> records = saveInfo.getRecords();
        if (BinaryUtils.isEmpty(records)) {
            return detailedResult;
        }
        List<ESCIClassInfo> classInfoList = classApiSvc.selectCiClassByIds(Lists.newArrayList(ciClassInfo.getId()));
        if (CollectionUtils.isEmpty(classInfoList)) {
            throw MessageException.i18n("BS_MNAME_CLASS_NOT_EXSIT");
        }
        ESCIClassInfo ciClass = classInfoList.get(0);
        // 校验CI属性-当前及父类属性
        List<ESCIAttrDefInfo> attrEsDefs = ciClass.getAttrDefs();
        Map<String, ESCIAttrDefInfo> attrDefInfoMap = attrEsDefs.stream().collect(Collectors.toMap(e -> e.getProName(), e -> e, (k1, k2) -> k1));
        SysUser loginUser = null;
        try {
            loginUser = SysUtil.getCurrentUserInfo();
        } catch (Exception e) {
            log.error("获取登陆用户失败，所有值取默认值");
        }
        Long sourceId = saveInfo.getSourceId() == null ? 1L : saveInfo.getSourceId();
        Long classId = saveInfo.getClassId() == null ? ciClassInfo.getId() : saveInfo.getClassId();
        List<ImportRowMessage> rowMessages = new ArrayList<>();

        List<SysUser> sysUserByCdt = userSvc.getAllUser();
        Map<String, SysUser> allUserMap = new HashMap<>();
        for (SysUser sysUser : sysUserByCdt) {
            allUserMap.put(sysUser.getUserName(),sysUser);
        }

        //excel导入设计关系数量不确定，这里直接查询全部
        List<CcCiClassInfo> ccCiClassInfos = iRltClassSvc.queryAllClasses(1L);
        //将ccCiClassInfos转成map，key是id，value是CcCiClassInfo
        Map<Long, CcCiClassInfo> ccCiClassInfoMap = new HashMap<>();
        for (CcCiClassInfo ccCiClassInfo : ccCiClassInfos) {
            ccCiClassInfoMap.put(ccCiClassInfo.getCiClass().getId(), ccCiClassInfo);
        }
        // 统计信息
        int successCount = 0;
        int failCount = 0;
        int totals = 0;
        int ignores = 0;
        int inserts = 0;
        int updates = 0;
        // 保存CI
        if (!BinaryUtils.isEmpty(records)) {
            totals = records.size();
            List<CcCiAttrDef> attrDefs = ciClassInfo.getCcAttrDefs();

            // When the 3D model switch is turned on, get whether the 3D classification is included in the attribute, and get the image path
            String _3DModelAttrName = "";
            Set<String> imagesPath = new HashSet<>(128);
            if (isShow3dAttribute) {
                _3DModelAttrName = this.getModelProStdName(attrDefs);
                List<CcImage> images = esImageSvc.getListByQuery(1, 9999, QueryBuilders.boolQuery()).getData();
                if (images.size() > 0) {
                    imagesPath = images.stream().map(CcImage::getImgPath).filter(path -> path != null && !"".equals(path.trim())).collect(Collectors.toSet());
                }
            }

            List<String> defNames = attrDefs.stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList());
            // 查询要更新的数据
            Map<String, CcCiInfo> ciCodeMap = new HashMap<>();
            // Map<Long, String> idToCode = new HashMap<>();
            Set<String> ciCodeSet = records.stream().map(CcCiRecord::getCiCode).filter(code -> !BinaryUtils.isEmpty(code)).collect(Collectors.toSet());
            if (!BinaryUtils.isEmpty(ciCodeSet)) {
                BoolQueryBuilder query = QueryBuilders.boolQuery();
                query.should(QueryBuilders.termsQuery("ciCode.keyword", ciCodeSet));
                List<CcCiInfo> esInfos = esCiSvc.getCIInfoPageByQuery(1, 5000, query, false).getData();
                if (!BinaryUtils.isEmpty(esInfos)) {
                    for (CcCiInfo esCiInfo : esInfos) {
                        ciCodeMap.put(esCiInfo.getCi().getCiCode(), esCiInfo);
                        // idToCode.put(esCiInfo.getId(), esCiInfo.getCiCode());
                    }
                }
            }
            // 获取关联资产配置
            List<CcCiAttrDef> releAssetAttrDefs = attrDefs.stream().filter(e -> e.getProType()
                    .equals(AttrNameKeyEnum.LINK_CI.getType())).collect(Collectors.toList());
            List<Long> releClassIdList = new ArrayList<>();
            Map<String, Long> attrMappingClassId = new HashMap<>();
            for (CcCiAttrDef releAssetAttrDef : releAssetAttrDefs) {
                String proDropSourceDef = releAssetAttrDef.getProDropSourceDef();
                if (!BinaryUtils.isEmpty(proDropSourceDef)) {
                    releClassIdList.add(Long.valueOf(proDropSourceDef));
                    attrMappingClassId.put(releAssetAttrDef.getProStdName(), Long.valueOf(proDropSourceDef));
                } else {
                    throw new BinaryException("关联资产【" + releAssetAttrDef.getProName() + "】配置异常,不可为空");
                }
            }
            Map<Long, Map<String, ESCIInfo>> classIdGroupCiMap = getClassCiGroup(releClassIdList);

            List<CcCiInfo> ciInfosList = new ArrayList<CcCiInfo>();
            List<Integer> hashCodes = new ArrayList<>();
            Map<String, Integer> keyToIndex = new HashMap<>();
            List<String> saveCodes = new ArrayList<>();
            List<String> savePrimaryKeys = new ArrayList<>();
            // CI校验-属性
            // 获取数据字典值
            Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
            Map<String, List<String>> dictValuesMap = this.getExterDictValues(domainId, attrDefs);
            Iterator<CcCiRecord> it = records.iterator();
            Map<String, ESCIOperateLog> updateLogMap = new HashMap<>();

            List<ESCIRltInfo> rltInfoList = new ArrayList<>();
            Set<String> delRltCiCodeSet= new HashSet<>();

            recordLoop:
            while (it.hasNext()) {
                boolean isUpdate = false;
                CcCiRecord record = it.next();
                Map<String, String> attrs = record.getAttrs();
                /**
                 * 3D model attribute picture path verification verification.
                 * When the 3D model switch is turned on,
                 * and the attributes include 3D type attributes,
                 * */
                if (isShow3dAttribute && !"".equals(_3DModelAttrName)) {
                    this._3DmodelAttributeImgPathVerification(_3DModelAttrName, attrs, imagesPath);
                }

                // 校验属性
                Map<String, Integer> errMsg = commSvc.validAttrs(attrDefs, attrs, true);
                getEnCodeNumByDef(ciClassInfo.getClassCode(), attrDefs, attrs);
                if (!BinaryUtils.isEmpty(errMsg)) {
                    List<ImportCellMessage> cellMessages = new ArrayList<>();
                    errMsg.forEach((msg, errType) -> {
                        ImportCellMessage cellMessage = new ImportCellMessage();
                        cellMessage.setErrorType(errType);
                        cellMessage.setErrorDesc(msg);
                        cellMessages.add(cellMessage);
                    });
                    ImportRowMessage rowMessage = new ImportRowMessage();
                    rowMessage.setRowNum(record.getIndex());
                    rowMessage.setMessageItems(cellMessages);
                    rowMessages.add(rowMessage);
                    failCount++;
                    it.remove();
                    continue;
                }
                Map<String, String> stdMap = toStdMap(attrs);
                // 校验转换导入关联资产类型
                boolean checkAssetFlag = false;
                if (!CollectionUtils.isEmpty(classIdGroupCiMap)) {
                    for (Entry<String, String> attrEntry : stdMap.entrySet()) {
                        String key = attrEntry.getKey();
                        String value = attrEntry.getValue();
                        if (attrMappingClassId.containsKey(key) && !BinaryUtils.isEmpty(value)) {
                            Long releClassId = attrMappingClassId.get(key);
                            List<JSONObject> valueList = new ArrayList<>();
                            Map<String, ESCIInfo> classPrimarykeyMap = classIdGroupCiMap.get(releClassId);
                            if (CollectionUtils.isEmpty(classPrimarykeyMap)) {
                                rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "关联属性[" + key + "]引用值[" + value + "]不存在"));
                                ignores++;
                                it.remove();
                                continue recordLoop;
                            }
                            String[] split = value.split(",");
                            for (int i = 0; i < split.length; i++) {
                                ESCIInfo esciInfo = classPrimarykeyMap.get(split[i]);
                                if (null != esciInfo) {
                                    JSONObject assetJson = new JSONObject();
                                    assetJson.put(CI_CODE, esciInfo.getCiCode());
                                    assetJson.put("primary", split[i]);
                                    valueList.add(assetJson);
                                } else {
                                    // 不存在
                                    checkAssetFlag = true;
                                    break;
                                }
                            }
                            if (!CollectionUtils.isEmpty(valueList)) {
                                stdMap.put(key, JSONObject.toJSONString(valueList));
                            }
                        }
                        if (checkAssetFlag) {
                            rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "关联资产[" + key + "]引用值[" + value + "]不存在"));
                            ignores++;
                            it.remove();
                            continue recordLoop;
                        }
                    }
                }
                for (CcCiAttrDef attrDef : attrDefs) {
                    if(attrDef.getProType().equals(ESPropertyType.PERSION.getValue())){
                        //人员类型
                        String s = stdMap.get(attrDef.getProStdName());
                        if(StringUtils.isNotBlank(s)){
                            String[] userNameArr = s.split(",");
                            List<JSONObject> valueList = new ArrayList<>();
                            for (String userName : userNameArr) {
                                SysUser sysUser = allUserMap.get(userName);
                                if(sysUser==null){
                                    rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "用户[" + userName + "]不存在，请检查用户名是否存在"));
                                    ignores++;
                                    it.remove();
                                    continue recordLoop;
                                }else {
                                    JSONObject assetJson = new JSONObject();
                                    assetJson.put("loginCode", sysUser.getLoginCode());
                                    assetJson.put("userName", sysUser.getUserName());
                                    valueList.add(assetJson);
                                }
                            }
                            if (!CollectionUtils.isEmpty(valueList)) {
                                stdMap.put(attrDef.getProStdName(), JSONObject.toJSONString(valueList));
                            }
                        }
                    }

                    if(attrDef.getProType().equals(ESPropertyType.PREFIX_INTEGER_CODE.getValue())){
                        //前缀整数编码类型
                        String s = stdMap.get(attrDef.getProStdName());
                        if(org.apache.commons.lang3.StringUtils.isBlank(s)){
                            continue ;
                        }
                        try {
                            long l = Long.parseLong(s);
                            stdMap.put(attrDef.getProStdName(), s);
                            continue ;
                        }catch (NumberFormatException numberFormatException){
                            log.info("转换数字异常，进行提取");
                        }
                        String defVal = attrDef.getDefVal();
                        String regex = "\\[(\\d+)\\]";
                        // 编译正则表达式
                        Pattern pattern = Pattern.compile(regex);
                        // 创建 Matcher 对象
                        Matcher matcher = pattern.matcher(s);
                        if (matcher.find()) {
                            // 获取匹配到的数字
                            String numberStr = matcher.group(1);
                            stdMap.put(attrDef.getProStdName(), numberStr);
                        } else {
                            log.error("未找到前缀编码的数字。" + s);
                            rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "属性[" + attrDef.getProStdName() + "]引用值[" + s + "]不正确"));
                            ignores++;
                            it.remove();
                            continue recordLoop;
                        }
                    }
                }
                // 校验数据字典类型
                if (!BinaryUtils.isEmpty(dictValuesMap)) {
                    Iterator<Entry<String, List<String>>> dictIt = dictValuesMap.entrySet().iterator();
                    while (dictIt.hasNext()) {
                        Entry<String, List<String>> next = dictIt.next();
                        String key = next.getKey();
                        List<String> values = next.getValue();
                        String val = stdMap.get(key);
                        ESCIAttrDefInfo esciAttrDefInfo = attrDefInfoMap.get(key);
                        List<String> group = esciAttrDefInfo.getGroup();
                        if (!CollectionUtils.isEmpty(group) && group.contains(CHECKBOX)) {
                            if (!BinaryUtils.isEmpty(val)) {
                                boolean flag = false;
                                String[] vals = new String[0];
                                try {
                                    vals = val.split(MULT_SYMBOL);
                                } catch (Exception e) {
                                    flag = true;
                                    log.error("属性[" + key + "]引用值[" + val + "]数据格式异常，多选为数组格式");
                                }
                                for (String value : vals) {
                                    if (record.getCiId() == null && !BinaryUtils.isEmpty(value) && !values.contains(value)) {
                                        flag = true;
                                        break;
                                    }
                                }
                                if (flag) {
                                    rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "属性[" + key + "]引用值[" + val + "]不存在"));
                                    ignores++;
                                    it.remove();
                                    continue recordLoop;
                                }
                            }
                        } else {
                            if (record.getCiId() == null && !BinaryUtils.isEmpty(val) && !values.contains(val)) {
                                rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "属性[" + key + "]引用值[" + val + "]不存在"));
                                ignores++;
                                it.remove();
                                continue recordLoop;
                            }
                        }
                    }
                }
                // 校验ciCode，同一分类下，ciCode相同更新
                String ciCode = record.getCiCode();
                if (!BinaryUtils.isEmpty(ciCode)) {
                    if (saveCodes.contains(ciCode)) {
                        rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.EXIST, "ciCode【" + ciCode + "】重复"));
                        ignores++;
                        it.remove();
                        continue;
                    } else if (ciCodeMap.containsKey(ciCode)) {
                        CcCiInfo esciInfo = ciCodeMap.get(ciCode);
                        // 不同分类下ciCode相同，视为重复，同分类下则更新
                        if (esciInfo.getCi().getClassId().longValue() == classId.longValue()) {
                            record.setCiId(esciInfo.getCi().getId());
                            updates++;
                            isUpdate = true;
                        } else {
                            rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.EXIST, "ciCode【" + ciCode + "】重复"));
                            ignores++;
                            it.remove();
                            continue;
                        }
                    }
                    saveCodes.add(ciCode);
                }
                List<String> ciPKAttrDefNames = CommUtil.getCiPKAttrDefNames(attrDefs);
                // 获取所属分类下的业务主键值的hashCode
                Integer hashCode;
                try {
                    hashCode = CommUtil.getCiMajorHashCode(attrs, ciPKAttrDefNames, saveInfo.getClassStdCode());
                } catch (Exception e) {
                    failCount++;
                    rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "业务主键生成失败"));
                    continue;
                }
                // 获取当前CI的业务主键值
                List<String> ciPrimaryKeys = CommUtil.getCiPrimaryKeys(saveInfo.getClassStdCode(), attrs, ciPKAttrDefNames);
                String primaryKey = JSON.toString(ciPrimaryKeys);
                if (savePrimaryKeys.contains(primaryKey)) {
                    ignores++;
                    rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.EXIST, "业务主键【" + ciPrimaryKeys + "】重复"));
                    continue;
                } else {
                    savePrimaryKeys.add(primaryKey);
                }
                CcCiInfo ciInfo = new CcCiInfo();
                // 属性过滤，只保存定义过的属性
                Iterator<String> itAttr = stdMap.keySet().iterator();
                while (itAttr.hasNext()) {
                    String attrName = itAttr.next();
                    // 属性不包含在定义的属性，并且不是下划线开头结尾的字段，去掉
                    if (!defNames.contains(attrName) && !attrName.matches("_([\\d\\D]*?)_")) {
                        itAttr.remove();
                    }
                }
                // CI 属性大小写转换
                ciInfo.setAttrs(stdMap);
                CcCi ci = new CcCi();
                if (isUpdate) {
                    CcCiInfo oldCi = ciCodeMap.get(ciCode);
                    ci.setCreateTime(oldCi.getCi().getCreateTime());
                    ci.setCiVersion(oldCi.getCi().getCiVersion());
                    ci.setLocalVersion(0L);
                    ci.setProcessApprovalStatus(oldCi.getCi().getProcessApprovalStatus());
                    ci.setPublicVersion(oldCi.getCi().getPublicVersion());
                    // 属性合并
                    Map<String, String> combineAttrs = new HashMap<>();
                    combineAttrs.putAll(oldCi.getAttrs());
                    combineAttrs.putAll(stdMap);
                    ciInfo.setAttrs(combineAttrs);
                    if (!CheckAttrUtil.checkAttrMapEqual(combineAttrs, oldCi.getAttrs())) {
                        ci.setCiVersion(String.valueOf(Long.parseLong(oldCi.getCi().getCiVersion()) + 1L));
                        ci.setPublicVersion(oldCi.getCi().getPublicVersion() + 1L);
                    }
                } else {
                    ci.setCiVersion(String.valueOf(1));
                    ci.setCreator(loginUser == null ? "system" : loginUser.getLoginCode());
                    ci.setCreateTime(ESUtil.getNumberDateTime());
                    ci.setLocalVersion(0L);
                    ci.setPublicVersion(1L);
                }
                ci.setId(record.getCiId() == null ? ESUtil.getUUID() : record.getCiId());
                ci.setCiCode(ciCode!=null?ciCode:ci.getId().toString());
                ci.setClassId(classId);
                ci.setSourceId(sourceId);
                ci.setDataStatus(1);
                ci.setHashCode(hashCode);
                ci.setCiPrimaryKey(primaryKey);
                ci.setCreateTime(ESUtil.getNumberDateTime());
                ci.setDomainId(1L);
                ciInfo.setCi(ci);
                ciInfosList.add(ciInfo);
                hashCodes.add(hashCode);
                keyToIndex.put(ci.getCiPrimaryKey(), record.getIndex());
                if (isUpdate) {
                    ESCIOperateLog ciLog = buildLogRecord(sourceId, SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE, attrDefs, ciCodeMap.get(ciCode).getAttrs(), stdMap, ciClassInfo.getClassName(), ci);
                    updateLogMap.put(ciCode, ciLog);
                }

                //处理关联资产类型关系的创建和删除
                if(isUpdate){
                    for (CcCiAttrDef releAssetAttrDef : releAssetAttrDefs) {
                        CcCiInfo esciInfo = ciCodeMap.get(ciCode);
                        Map<String, String> oldAttrs = esciInfo.getAttrs();
                        String oldLinkCiStr = oldAttrs.get(releAssetAttrDef.getProStdName());
                        String linkCiStr = stdMap.get(releAssetAttrDef.getProStdName());
                        Long constructRltId = releAssetAttrDef.getConstructRltId();
                        String sourceOrTarget = releAssetAttrDef.getSourceOrTarget();
                        if (constructRltId == null || org.apache.commons.lang3.StringUtils.isBlank(sourceOrTarget)) {
                            continue;
                        }
                        CcCiClassInfo linkClass = ccCiClassInfoMap.get(constructRltId);
                        if (linkClass==null) {
                            continue;
                        }
                        if(org.apache.commons.lang3.StringUtils.isNotBlank(linkCiStr)&& org.apache.commons.lang3.StringUtils.isBlank(oldLinkCiStr)){
                            //新增
                            String s = stdMap.get(releAssetAttrDef.getProStdName());
                            if(org.apache.commons.lang3.StringUtils.isNotBlank(s)) {
                                JSONArray objects = com.alibaba.fastjson.JSON.parseArray(s);
                                for (int i = 0; i < objects.size(); i++) {
                                    String linkCiCode = objects.getJSONObject(i).getString("ciCode");
                                    ESCIRltInfo esciRltInfo = new ESCIRltInfo();
                                    esciRltInfo.setClassId(linkClass.getCiClass().getId());
                                    if ("target".equalsIgnoreCase(sourceOrTarget)) {
                                        String key = ciInfo.getCi().getCiCode() + "_" + linkClass.getCiClass().getId() + "_" + linkCiCode;
                                        esciRltInfo.setCiCode(key);
                                        esciRltInfo.setUniqueCode("UK_" + key);
                                        esciRltInfo.setSourceCiCode(ciInfo.getCi().getCiCode());
                                        esciRltInfo.setSourceClassId(ciInfo.getCi().getClassId());
                                        esciRltInfo.setTargetCiCode(linkCiCode);
                                        esciRltInfo.setTargetClassId(Long.parseLong(releAssetAttrDef.getProDropSourceDef()));
                                    } else {
                                        String key =  linkCiCode+ "_" + linkClass.getCiClass().getId() + "_" + ciInfo.getCi().getCiCode();
                                        esciRltInfo.setCiCode(key);
                                        esciRltInfo.setUniqueCode("UK_" + key);
                                        esciRltInfo.setSourceCiCode(linkCiCode);
                                        esciRltInfo.setSourceClassId(Long.parseLong(releAssetAttrDef.getProDropSourceDef()));
                                        esciRltInfo.setTargetCiCode(ciInfo.getCi().getCiCode());
                                        esciRltInfo.setTargetClassId(ciInfo.getCi().getClassId());
                                    }
                                    rltInfoList.add(esciRltInfo);
                                }
                            }
                        }else if(org.apache.commons.lang3.StringUtils.isNotBlank(oldLinkCiStr)&& org.apache.commons.lang3.StringUtils.isBlank(linkCiStr)){
                            //全部删除
                            JSONArray objects = com.alibaba.fastjson.JSON.parseArray(oldLinkCiStr);
                            for (int i = 0; i < objects.size(); i++) {
                                String linkCiCode = objects.getJSONObject(i).getString("ciCode");
                                if ("target".equalsIgnoreCase(sourceOrTarget)) {
                                    String key = ciInfo.getCi().getCiCode() + "_" + linkClass.getCiClass().getId() + "_" + linkCiCode;
                                    delRltCiCodeSet.add(key);
                                } else {
                                    String key =  linkCiCode+ "_" + linkClass.getCiClass().getId() + "_" + ciInfo.getCi().getCiCode();
                                    delRltCiCodeSet.add(key);
                                }
                            }
                        }else if(org.apache.commons.lang3.StringUtils.isNotBlank(oldLinkCiStr)&& org.apache.commons.lang3.StringUtils.isNotBlank(linkCiStr)){
                            //判断
                            JSONArray oldAttrJsonArr = com.alibaba.fastjson.JSON.parseArray(oldLinkCiStr);
                            JSONArray newAttrJsonArr = com.alibaba.fastjson.JSON.parseArray(linkCiStr);
                            Set<String> oldCiCodes = new HashSet<>();
                            for (int i=0;i< oldAttrJsonArr.size();i++){
                                oldCiCodes.add(oldAttrJsonArr.getJSONObject(i).getString("ciCode"));
                            }
                            Set<String> newCiCodes = new HashSet<>();
                            for (int i=0;i< newAttrJsonArr.size();i++){
                                newCiCodes.add(newAttrJsonArr.getJSONObject(i).getString("ciCode"));
                            }

                            Sets.SetView<String> needAddCodes = Sets.difference(newCiCodes, oldCiCodes);
                            Sets.SetView<String> needDelCodes = Sets.difference(oldCiCodes,newCiCodes);
                            //新增
                            for (String needAddCode : needAddCodes) {
                                ESCIRltInfo esciRltInfo = new ESCIRltInfo();
                                esciRltInfo.setClassId(linkClass.getCiClass().getId());
                                if ("target".equalsIgnoreCase(sourceOrTarget)) {
                                    String key = ciInfo.getCi().getCiCode() + "_" + linkClass.getCiClass().getId() + "_" + ciCode;
                                    esciRltInfo.setCiCode(key);
                                    esciRltInfo.setUniqueCode("UK_" + key);
                                    esciRltInfo.setSourceCiCode(ciInfo.getCi().getCiCode());
                                    esciRltInfo.setSourceClassId(ciInfo.getCi().getClassId());
                                    esciRltInfo.setTargetCiCode(needAddCode);
                                    esciRltInfo.setTargetClassId(Long.parseLong(releAssetAttrDef.getProDropSourceDef()));
                                } else {
                                    String key =  needAddCode+ "_" + linkClass.getCiClass().getId() + "_" + ciInfo.getCi().getCiCode();
                                    esciRltInfo.setCiCode(key);
                                    esciRltInfo.setUniqueCode("UK_" + key);
                                    esciRltInfo.setSourceCiCode(needAddCode);
                                    esciRltInfo.setSourceClassId(Long.parseLong(releAssetAttrDef.getProDropSourceDef()));
                                    esciRltInfo.setTargetCiCode(ciInfo.getCi().getCiCode());
                                    esciRltInfo.setTargetClassId(ciInfo.getCi().getClassId());
                                }
                                rltInfoList.add(esciRltInfo);
                            }
                            //删除
                            for (String needDelCode : needDelCodes) {
                                if ("target".equalsIgnoreCase(sourceOrTarget)) {
                                    String key = ciInfo.getCi().getCiCode() + "_" + linkClass.getCiClass().getId() + "_" + needDelCode;
                                    delRltCiCodeSet.add(key);
                                } else {
                                    String key =  needDelCode+ "_" + linkClass.getCiClass().getId() + "_" + ciInfo.getCi().getCiCode();
                                    delRltCiCodeSet.add(key);
                                }
                            }
                        }
                    }
                }else {
                    //新增直接建立新关系
                    for (CcCiAttrDef releAssetAttrDef : releAssetAttrDefs) {
                        String s = stdMap.get(releAssetAttrDef.getProStdName());
                        Long constructRltId = releAssetAttrDef.getConstructRltId();
                        String sourceOrTarget = releAssetAttrDef.getSourceOrTarget();
                        if (constructRltId == null || org.apache.commons.lang3.StringUtils.isBlank(sourceOrTarget)) {
                            continue;
                        }
                        CcCiClassInfo linkClass = ccCiClassInfoMap.get(constructRltId);
                        if(org.apache.commons.lang3.StringUtils.isNotBlank(s)){
                            JSONArray objects = com.alibaba.fastjson.JSON.parseArray(s);
                            for (int i =0;i<objects.size();i++){
                                String linkCiCode = objects.getJSONObject(i).getString("ciCode");
                                ESCIRltInfo esciRltInfo = new ESCIRltInfo();
                                esciRltInfo.setClassId(linkClass.getCiClass().getId());
                                if ("target".equalsIgnoreCase(sourceOrTarget)) {
                                    String key = ciInfo.getCi().getCiCode() + "_" + linkClass.getCiClass().getId() + "_" + linkCiCode;
                                    esciRltInfo.setCiCode(key);
                                    esciRltInfo.setUniqueCode("UK_" + key);
                                    esciRltInfo.setSourceCiCode(ciInfo.getCi().getCiCode());
                                    esciRltInfo.setSourceClassId(ciInfo.getCi().getClassId());
                                    esciRltInfo.setTargetCiCode(linkCiCode);
                                    esciRltInfo.setTargetClassId(Long.parseLong(releAssetAttrDef.getProDropSourceDef()));
                                } else {
                                    String key =  linkCiCode+ "_" + linkClass.getCiClass().getId() + "_" + ciInfo.getCi().getCiCode();
                                    esciRltInfo.setCiCode(key);
                                    esciRltInfo.setUniqueCode("UK_" + key);
                                    esciRltInfo.setSourceCiCode(linkCiCode);
                                    esciRltInfo.setSourceClassId(Long.parseLong(releAssetAttrDef.getProDropSourceDef()));
                                    esciRltInfo.setTargetCiCode(ciInfo.getCi().getCiCode());
                                    esciRltInfo.setTargetClassId(ciInfo.getCi().getClassId());
                                }
                                rltInfoList.add(esciRltInfo);
                            }
                        }
                    }
                }
            }
            List<String> repeatKeys = new ArrayList<>();
            // 校验hashCode是否重复
            Map<String, Integer> res = this.checkRecordsByHashCode(ciInfosList, repeatKeys, updateLogMap);
            ignores += res.get("ignore") == null ? 0 : res.get("ignore");
            updates += res.get("update") == null ? 0 : res.get("update");
            if (!BinaryUtils.isEmpty(repeatKeys)) {
                for (String str : repeatKeys) {
                    rowMessages.add(this.buildRowMessage(keyToIndex.get(str), CheckAttrUtil.EXIST, "业务主键【" + str + "】重复"));
                }
            }
            // 保存CI操作日志
            List<ESCIOperateLog> ciLogs = new ArrayList<ESCIOperateLog>(updateLogMap.values());
            for (ESCIOperateLog ciLog : ciLogs) {
                ciLog.setSourceId(sourceId);
                ciLog.setCiClassName(ciClassInfo.getClassName());
                ciLog.setProNames(attrDefs.stream().map(CcCiAttrDef::getProName).collect(Collectors.toList()));
                ciLog.setOperator(loginUser == null ? "system" : loginUser.getLoginCode());
            }
            Set<String> keySet = updateLogMap.keySet();
            if (ciInfosList.size() > keySet.size()) {
                ciInfosList.forEach(ciInfo -> {
                    if (BinaryUtils.isEmpty(ciInfo.getCi().getCiCode())) {
                        ciInfo.getCi().setCiCode(String.valueOf(ciInfo.getCi().getId()));
                    }
                    String ciCode = ciInfo.getCi().getCiCode();
                    if (!keySet.contains(ciCode)) {
                        ciLogs.add(buildLogRecord(sourceId, SysUtil.StaticUtil.LOG_DYNAMIC_INSERT, attrDefs, null, ciInfo.getAttrs(), ciClassInfo.getClassName(), ciInfo.getCi()));
                    }
                });
            }
            Map<String, Object> saveOrUpdateMsg = esCiSvc.saveOrUpdateCIBatch(ciInfosList, false);
            //加入文件目录
            if ( !BinaryUtils.isEmpty(ciInfosList)&&excelInfoDto.getDirectoryId()!=null){
                // 提取所有 ciCode
                List<String> ciCodes= new ArrayList<>();
                for (CcCiInfo ccCiInfo : ciInfosList) {
                    if (!BinaryUtils.isEmpty(ccCiInfo)){
                        ciCodes.add(ccCiInfo.getCi().getCiCode());
                    }
                }
                List<EamDirectoryObjectAssociation> existingAssociations = eamDirectoryObjectAssociationDao.getListByQuery(QueryBuilders.termsQuery("ciCode.keyword", ciCodes));
                // 构建 ciCode 到 EamDirectoryObjectAssociation 的映射
                Map<String, EamDirectoryObjectAssociation> associationMap = existingAssociations.stream()
                        .collect(Collectors.toMap(EamDirectoryObjectAssociation::getCiCode, Function.identity()));
                 // 处理每个 ccCiInfo
                List<EamDirectoryObjectAssociation> toSaveOrUpdate = new ArrayList<>();
                for (CcCiInfo ccCiInfo : ciInfosList) {
                    if (BinaryUtils.isEmpty(ccCiInfo)) {
                        continue;
                    }
                    String ciCode = ccCiInfo.getCi().getCiCode();
                    EamDirectoryObjectAssociation eamDirectoryObjectAssociation = associationMap.getOrDefault(ciCode, new EamDirectoryObjectAssociation());
                    if (eamDirectoryObjectAssociation.getId()==null) {
                        eamDirectoryObjectAssociation.setCiCode(ciCode);
                        eamDirectoryObjectAssociation.setId(ESUtil.getUUID());
                        eamDirectoryObjectAssociation.setDirectoryId(excelInfoDto.getDirectoryId());
                        toSaveOrUpdate.add(eamDirectoryObjectAssociation);
                    }
                }

                // 批量保存或更新
                eamDirectoryObjectAssociationDao.saveOrUpdateBatch(toSaveOrUpdate);
            }

            // if (!BinaryUtils.isEmpty(saveOrUpdateMsg.get("failCount"))) {
            // failCount += (Integer) saveOrUpdateMsg.get("failCount");
            // }
            inserts = (totals - failCount - updates - ignores);
            successCount = inserts + updates;
            if (!BinaryUtils.isEmpty(saveOrUpdateMsg.get("errMessge"))) {
                String errStr = JSON.toString(saveOrUpdateMsg.get("errMessge"));
                // 入库失败
                rowMessages.add(this.buildRowMessage(1, CheckAttrUtil.FAILURE, "入库失败：" + errStr));
            }

            //保存关系
            if (!CollectionUtils.isEmpty(rltInfoList)) {
                ciRltSvc.bindBatchCiRlt(rltInfoList, SysUtil.getCurrentUserInfo().getLoginCode());
            }
            if(!CollectionUtils.isEmpty(delRltCiCodeSet)){
                ciRltSvc.delRltByIdsOrRltCodes(null,delRltCiCodeSet,null);
            }

            this.saveCIOperateLogBatch(ciLogs);
            // saveMessage.add(saveOrUpdateMsg);
        }
        // 汇总整理结果
        detailedResult.setSuccessNum(successCount);
        detailedResult.setFailNum(failCount);
        detailedResult.setInsertNum(inserts);
        detailedResult.setUpdateNum(updates);
        detailedResult.setIgnoreNum(ignores);
        detailedResult.setTotalNum(totals);
        detailedResult.setRowMessages(rowMessages);
        return detailedResult;
    }

    private ImportSheetMessage saveOrUpdateFlowCiBatchForExcelImport(CiClassSaveInfo saveInfo, ESCIClassInfo ciClassInfo,Map<String, String> rootFlowLevel) {
        BinaryUtils.checkEmpty(saveInfo, "saveInfo");
        BinaryUtils.checkEmpty(ciClassInfo, "classInfo");
        BinaryUtils.checkEmpty(ciClassInfo.getId(), "classId");
        BinaryUtils.checkEmpty(ciClassInfo.getCcAttrDefs(), "attrDefs");
        ImportSheetMessage detailedResult = ImportSheetMessage.builder().className(ciClassInfo.getClassName()).build();
        // 待保存数据为空，直接返回
        List<CcCiRecord> records = saveInfo.getRecords();
        if (BinaryUtils.isEmpty(records)) {
            return detailedResult;
        }
        List<ESCIClassInfo> classInfoList = classApiSvc.selectCiClassByIds(Lists.newArrayList(ciClassInfo.getId()));
        if (CollectionUtils.isEmpty(classInfoList)) {
            throw MessageException.i18n("BS_MNAME_CLASS_NOT_EXSIT");
        }
        ESCIClassInfo ciClass = classInfoList.get(0);
        // 校验CI属性-当前及父类属性
        List<ESCIAttrDefInfo> attrEsDefs = ciClass.getAttrDefs();
        Map<String, ESCIAttrDefInfo> attrDefInfoMap = attrEsDefs.stream().collect(Collectors.toMap(e -> e.getProName(), e -> e, (k1, k2) -> k1));
        SysUser loginUser = null;
        try {
            loginUser = SysUtil.getCurrentUserInfo();
        } catch (Exception e) {
            log.error("获取登陆用户失败，所有值取默认值");
        }
        Long sourceId = saveInfo.getSourceId() == null ? 1L : saveInfo.getSourceId();
        Long classId = saveInfo.getClassId() == null ? ciClassInfo.getId() : saveInfo.getClassId();
        List<ImportRowMessage> rowMessages = new ArrayList<>();

        List<SysUser> sysUserByCdt = userSvc.getAllUser();
        Map<String, SysUser> allUserMap = new HashMap<>();
        for (SysUser sysUser : sysUserByCdt) {
            allUserMap.put(sysUser.getUserName(),sysUser);
        }

        // 统计信息
        int successCount = 0;
        int failCount = 0;
        int totals = 0;
        int ignores = 0;
        int inserts = 0;
        int updates = 0;
        // 保存CI
        if (!BinaryUtils.isEmpty(records)) {
            totals = records.size();
            List<CcCiAttrDef> attrDefs = ciClassInfo.getCcAttrDefs();

            // When the 3D model switch is turned on, get whether the 3D classification is included in the attribute, and get the image path
            String _3DModelAttrName = "";
            Set<String> imagesPath = new HashSet<>(128);
            if (isShow3dAttribute) {
                _3DModelAttrName = this.getModelProStdName(attrDefs);
                List<CcImage> images = esImageSvc.getListByQuery(1, 9999, QueryBuilders.boolQuery()).getData();
                if (images.size() > 0) {
                    imagesPath = images.stream().map(CcImage::getImgPath).filter(path -> path != null && !"".equals(path.trim())).collect(Collectors.toSet());
                }
            }

            List<String> defNames = attrDefs.stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList());
            // 查询要更新的数据
            Map<String, CcCiInfo> ciCodeMap = new HashMap<>();
            // Map<Long, String> idToCode = new HashMap<>();
            Set<String> ciCodeSet = records.stream().map(CcCiRecord::getCiCode).filter(code -> !BinaryUtils.isEmpty(code)).collect(Collectors.toSet());
            if (!BinaryUtils.isEmpty(ciCodeSet)) {
                BoolQueryBuilder query = QueryBuilders.boolQuery();
                query.should(QueryBuilders.termsQuery("ciCode.keyword", ciCodeSet));
                List<CcCiInfo> esInfos = esCiSvc.getCIInfoPageByQuery(1, 5000, query, false).getData();
                if (!BinaryUtils.isEmpty(esInfos)) {
                    for (CcCiInfo esCiInfo : esInfos) {
                        ciCodeMap.put(esCiInfo.getCi().getCiCode(), esCiInfo);
                        // idToCode.put(esCiInfo.getId(), esCiInfo.getCiCode());
                    }
                }
            }
            // 获取关联资产配置
            List<CcCiAttrDef> releAssetAttrDefs = attrDefs.stream().filter(e -> e.getProType()
                    .equals(AttrNameKeyEnum.LINK_CI.getType())).collect(Collectors.toList());
            List<Long> releClassIdList = new ArrayList<>();
            Set<Long> releRltClassIdList = new HashSet<>();
            Map<String, Long> attrMappingClassId = new HashMap<>();
            for (CcCiAttrDef releAssetAttrDef : releAssetAttrDefs) {
                String proDropSourceDef = releAssetAttrDef.getProDropSourceDef();
                if (!BinaryUtils.isEmpty(proDropSourceDef)) {
                    releClassIdList.add(Long.valueOf(proDropSourceDef));
                    attrMappingClassId.put(releAssetAttrDef.getProStdName(), Long.valueOf(proDropSourceDef));
                } else {
                    throw new BinaryException("关联资产【" + releAssetAttrDef.getProName() + "】配置异常,不可为空");
                }
                Long constructRltId = releAssetAttrDef.getConstructRltId();
                if(constructRltId!=null){
                    releRltClassIdList.add(constructRltId);
                }
            }
            HashMap<Long, CcCiClassInfo> rltClassHashMap = new HashMap<>();
            if(!CollectionUtils.isEmpty(releRltClassIdList)){
                CCcCiClass cdt = new CCcCiClass();
                cdt.setIds(releRltClassIdList.toArray(new Long[0]));
                List<CcCiClassInfo> rltClasses = iRltClassSvc.getRltClassByCdt(cdt);
                for (CcCiClassInfo rltClassInfo : rltClasses) {
                    rltClassHashMap.put(rltClassInfo.getCiClass().getId(), rltClassInfo);
                }
            }

            Map<Long, Map<String, ESCIInfo>> classIdGroupCiMap = getClassCiGroup(releClassIdList);

            List<CcCiInfo> ciInfosList = new ArrayList<CcCiInfo>();
            List<Integer> hashCodes = new ArrayList<>();
            Map<String, Integer> keyToIndex = new HashMap<>();
            List<String> saveCodes = new ArrayList<>();
            List<String> savePrimaryKeys = new ArrayList<>();
            // CI校验-属性
            // 获取数据字典值
            Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
            Map<String, List<String>> dictValuesMap = this.getExterDictValues(domainId, attrDefs);
            Iterator<CcCiRecord> it = records.iterator();
            Map<String, ESCIOperateLog> updateLogMap = new HashMap<>();

            List<ESCIRltInfo> rltInfoList = new ArrayList<>();
            Set<String> delRltCiCodeSet= new HashSet<>();

            recordLoop:
            while (it.hasNext()) {
                boolean isUpdate = false;
                CcCiRecord record = it.next();
                Map<String, String> attrs = record.getAttrs();
                /**
                 * 3D model attribute picture path verification verification.
                 * When the 3D model switch is turned on,
                 * and the attributes include 3D type attributes,
                 * */
                if (isShow3dAttribute && !"".equals(_3DModelAttrName)) {
                    this._3DmodelAttributeImgPathVerification(_3DModelAttrName, attrs, imagesPath);
                }

                // 校验属性
                Map<String, Integer> errMsg = commSvc.validAttrs(attrDefs, attrs, true);
                getEnCodeNumByDef(ciClassInfo.getClassCode(), attrDefs, attrs);
                if (!BinaryUtils.isEmpty(errMsg)) {
                    List<ImportCellMessage> cellMessages = new ArrayList<>();
                    errMsg.forEach((msg, errType) -> {
                        ImportCellMessage cellMessage = new ImportCellMessage();
                        cellMessage.setErrorType(errType);
                        cellMessage.setErrorDesc(msg);
                        cellMessages.add(cellMessage);
                    });
                    ImportRowMessage rowMessage = new ImportRowMessage();
                    rowMessage.setRowNum(record.getIndex());
                    rowMessage.setMessageItems(cellMessages);
                    rowMessages.add(rowMessage);
                    failCount++;
                    it.remove();
                    continue;
                }
                Map<String, String> stdMap = toStdMap(attrs);
                // 校验转换导入关联资产类型
                boolean checkAssetFlag = false;
                if (!CollectionUtils.isEmpty(classIdGroupCiMap)) {
                    for (Entry<String, String> attrEntry : stdMap.entrySet()) {
                        String key = attrEntry.getKey();
                        String value = attrEntry.getValue();
                        if (attrMappingClassId.containsKey(key) && !BinaryUtils.isEmpty(value)) {
                            Long releClassId = attrMappingClassId.get(key);
                            List<JSONObject> valueList = new ArrayList<>();
                            Map<String, ESCIInfo> classPrimarykeyMap = classIdGroupCiMap.get(releClassId);
                            if (CollectionUtils.isEmpty(classPrimarykeyMap)) {
                                rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "关联属性[" + key + "]引用值[" + value + "]不存在"));
                                ignores++;
                                it.remove();
                                continue recordLoop;
                            }
                            String[] split = value.split(",");
                            for (int i = 0; i < split.length; i++) {
                                ESCIInfo esciInfo = classPrimarykeyMap.get(split[i]);
                                if (null != esciInfo) {
                                    if (ciClass.getClassCode().equalsIgnoreCase("流程组")
                                            && key.equalsIgnoreCase("上级架构") &&
                                            !stdMap.get("流程级别").equalsIgnoreCase(rootFlowLevel.get("1"))) {
                                        //判断流程是否规范
                                        String parentFlowLevel = esciInfo.getAttrs().get("流程级别").toString();
                                        String s = stdMap.get("流程级别");
                                        String currentLevel = "0";
                                        String parentLevel = "0";
                                        for (Entry<String, String> stringStringEntry : rootFlowLevel.entrySet()) {
                                            String value1 = stringStringEntry.getValue();
                                            if (value1.equalsIgnoreCase(parentFlowLevel)) {
                                                parentLevel = stringStringEntry.getKey();
                                            }
                                            if (value1.equalsIgnoreCase(s)) {
                                                currentLevel = stringStringEntry.getKey();
                                            }
                                        }
                                        if (Integer.parseInt(currentLevel) <= Integer.parseInt(parentLevel)) {
                                            //记录上级为空提示
                                            rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "关联资产[" + key + "]跨流程级别"));
                                            ignores++;
                                            it.remove();
                                            continue recordLoop;
                                        } else if (Integer.parseInt(currentLevel) - Integer.parseInt(parentLevel) != 1) {
                                            //记录上级为空提示
                                            rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "关联资产[" + key + "]未高于当前流程级别"));
                                            ignores++;
                                            it.remove();
                                            continue recordLoop;
                                        }
                                    }
                                    JSONObject assetJson = new JSONObject();
                                    assetJson.put(CI_CODE, esciInfo.getCiCode());
                                    assetJson.put("primary", split[i]);
                                    valueList.add(assetJson);
                                } else {
                                    // 不存在
                                    checkAssetFlag = true;
                                    break;
                                }
                            }
                            if (!CollectionUtils.isEmpty(valueList)) {
                                stdMap.put(key, JSONObject.toJSONString(valueList));
                            }
                        } else {
                            if (ciClass.getClassCode().equalsIgnoreCase("流程组")
                                    && key.equalsIgnoreCase("上级架构") &&
                                    !stdMap.get("流程级别").equalsIgnoreCase(rootFlowLevel.get("1"))) {
                                //记录上级为空提示
                                rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "关联资产[" + key + "]为空"));
                                ignores++;
                                it.remove();
                                continue recordLoop;
                            }
                        }
                        if (checkAssetFlag) {
                            if (ciClass.getClassCode().equalsIgnoreCase("流程组")) {
                                rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "关联资产[" + key + "]引用值[" + value + "]不存在,请检查流程级别或上级架构是否填写正确"));
                            } else {
                                rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "关联资产[" + key + "]引用值[" + value + "]不存在"));
                            }
                            ignores++;
                            it.remove();
                            continue recordLoop;
                        }
                    }
                }
                for (CcCiAttrDef attrDef : attrDefs) {
                    if(attrDef.getProType().equals(ESPropertyType.PERSION.getValue())){
                        //人员类型
                        String s = stdMap.get(attrDef.getProStdName());
                        if(StringUtils.isNotBlank(s)){
                            String[] userNameArr = s.split(",");
                            List<JSONObject> valueList = new ArrayList<>();
                            for (String userName : userNameArr) {
                                SysUser sysUser = allUserMap.get(userName);
                                if(sysUser==null){
                                    rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "用户[" + userName + "]不存在，请检查用户名是否存在"));
                                    ignores++;
                                    it.remove();
                                    continue recordLoop;
                                }else {
                                    JSONObject assetJson = new JSONObject();
                                    assetJson.put("loginCode", sysUser.getLoginCode());
                                    assetJson.put("userName", sysUser.getUserName());
                                    valueList.add(assetJson);
                                }
                            }
                            if (!CollectionUtils.isEmpty(valueList)) {
                                stdMap.put(attrDef.getProStdName(), JSONObject.toJSONString(valueList));
                            }
                        }
                    }
                }
                // 校验数据字典类型
                if (!BinaryUtils.isEmpty(dictValuesMap)) {
                    Iterator<Entry<String, List<String>>> dictIt = dictValuesMap.entrySet().iterator();
                    while (dictIt.hasNext()) {
                        Entry<String, List<String>> next = dictIt.next();
                        String key = next.getKey();
                        List<String> values = next.getValue();
                        String val = stdMap.get(key);
                        ESCIAttrDefInfo esciAttrDefInfo = attrDefInfoMap.get(key);
                        List<String> group = esciAttrDefInfo.getGroup();
                        if (!CollectionUtils.isEmpty(group) && group.contains("多选")) {
                            if (!BinaryUtils.isEmpty(val)) {
                                boolean flag = false;
                                List<String> valList = new ArrayList<>();
                                try {
                                    valList = JSONObject.parseArray(val, String.class);
                                } catch (Exception e) {
                                    log.error("属性[" + key + "]引用值[" + val + "]数据格式异常，多选为数组格斯");
                                    valList.add(val);
                                    stdMap.put(key, JSONObject.toJSONString(valList));
                                }
                                for (String value : valList) {
                                    if (record.getCiId() == null && !BinaryUtils.isEmpty(value) && !values.contains(value)) {
                                        flag = true;
                                        break;
                                    }
                                }
                                if (flag) {
                                    rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "属性[" + key + "]引用值[" + val + "]不存在"));
                                    ignores++;
                                    it.remove();
                                    continue recordLoop;
                                }
                            }
                        } else {
                            if (record.getCiId() == null && !BinaryUtils.isEmpty(val) && !values.contains(val)) {
                                rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "属性[" + key + "]引用值[" + val + "]不存在"));
                                ignores++;
                                it.remove();
                                continue recordLoop;
                            }
                        }
                    }
                }
                // 校验ciCode，同一分类下，ciCode相同更新
                String ciCode = record.getCiCode();
                if (!BinaryUtils.isEmpty(ciCode)) {
                    if (saveCodes.contains(ciCode)) {
                        rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.EXIST, "ciCode【" + ciCode + "】重复"));
                        ignores++;
                        it.remove();
                        continue;
                    } else if (ciCodeMap.containsKey(ciCode)) {
                        CcCiInfo esciInfo = ciCodeMap.get(ciCode);
                        // 不同分类下ciCode相同，视为重复，同分类下则更新
                        if (esciInfo.getCi().getClassId().longValue() == classId.longValue()) {
                            record.setCiId(esciInfo.getCi().getId());
                            updates++;
                            isUpdate = true;
                        } else {
                            rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.EXIST, "ciCode【" + ciCode + "】重复"));
                            ignores++;
                            it.remove();
                            continue;
                        }
                    }
                    saveCodes.add(ciCode);
                }
                List<String> ciPKAttrDefNames = CommUtil.getCiPKAttrDefNames(attrDefs);
                // 获取所属分类下的业务主键值的hashCode
                Integer hashCode;
                try {
                    hashCode = CommUtil.getCiMajorHashCode(attrs, ciPKAttrDefNames, saveInfo.getClassStdCode());
                } catch (Exception e) {
                    failCount++;
                    rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "业务主键生成失败"));
                    continue;
                }
                // 获取当前CI的业务主键值
                List<String> ciPrimaryKeys = CommUtil.getCiPrimaryKeys(saveInfo.getClassStdCode(), attrs, ciPKAttrDefNames);
                String primaryKey = JSON.toString(ciPrimaryKeys);
                if (savePrimaryKeys.contains(primaryKey)) {
                    ignores++;
                    rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.EXIST, "业务主键【" + ciPrimaryKeys + "】重复"));
                    continue;
                } else {
                    savePrimaryKeys.add(primaryKey);
                }
                CcCiInfo ciInfo = new CcCiInfo();
                // 属性过滤，只保存定义过的属性
                Iterator<String> itAttr = stdMap.keySet().iterator();
                while (itAttr.hasNext()) {
                    String attrName = itAttr.next();
                    // 属性不包含在定义的属性，并且不是下划线开头结尾的字段，去掉
                    if (!defNames.contains(attrName) && !attrName.matches("_([\\d\\D]*?)_")) {
                        itAttr.remove();
                    }
                }
                // CI 属性大小写转换
                ciInfo.setAttrs(stdMap);
                CcCi ci = new CcCi();
                if (isUpdate) {
                    CcCiInfo oldCi = ciCodeMap.get(ciCode);
                    ci.setCreateTime(oldCi.getCi().getCreateTime());
                    ci.setCiVersion(oldCi.getCi().getCiVersion());
                    ci.setLocalVersion(0L);
                    ci.setProcessApprovalStatus(oldCi.getCi().getProcessApprovalStatus());
                    ci.setPublicVersion(oldCi.getCi().getPublicVersion());
                    // 属性合并
                    Map<String, String> combineAttrs = new HashMap<>();
                    combineAttrs.putAll(oldCi.getAttrs());
                    combineAttrs.putAll(stdMap);
                    ciInfo.setAttrs(combineAttrs);
                    if (!CheckAttrUtil.checkAttrMapEqual(combineAttrs, oldCi.getAttrs())) {
                        ci.setCiVersion(String.valueOf(Long.parseLong(oldCi.getCi().getCiVersion()) + 1L));
                        ci.setPublicVersion(oldCi.getCi().getPublicVersion() + 1L);
                    }
                } else {
                    ci.setCiVersion(String.valueOf(1));
                    ci.setCreator(loginUser == null ? "system" : loginUser.getLoginCode());
                    ci.setCreateTime(ESUtil.getNumberDateTime());
                    ci.setLocalVersion(0L);
                    ci.setPublicVersion(1L);
                }
                ci.setId(record.getCiId() == null ? ESUtil.getUUID() : record.getCiId());
                ci.setCiCode(ciCode!=null?ciCode:ci.getId().toString());
                ci.setClassId(classId);
                ci.setSourceId(sourceId);
                ci.setDataStatus(1);
                ci.setHashCode(hashCode);
                ci.setCiPrimaryKey(primaryKey);
                ci.setCreateTime(ESUtil.getNumberDateTime());
                ci.setDomainId(1L);
                ciInfo.setCi(ci);
                ciInfosList.add(ciInfo);
                hashCodes.add(hashCode);
                keyToIndex.put(ci.getCiPrimaryKey(), record.getIndex());
                if (isUpdate) {
                    ESCIOperateLog ciLog = buildLogRecord(sourceId, SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE, attrDefs, ciCodeMap.get(ciCode).getAttrs(), stdMap, ciClassInfo.getClassName(), ci);
                    updateLogMap.put(ciCode, ciLog);
                }

                //处理关联资产类型关系的创建和删除
                if(isUpdate){
                    for (CcCiAttrDef releAssetAttrDef : releAssetAttrDefs) {
                        CcCiInfo esciInfo = ciCodeMap.get(ciCode);
                        Map<String, String> oldAttrs = esciInfo.getAttrs();
                        String oldLinkCiStr = oldAttrs.get(releAssetAttrDef.getProStdName());
                        String linkCiStr = stdMap.get(releAssetAttrDef.getProStdName());
                        Long constructRltId = releAssetAttrDef.getConstructRltId();
                        String sourceOrTarget = releAssetAttrDef.getSourceOrTarget();
                        if (constructRltId == null || org.apache.commons.lang3.StringUtils.isBlank(sourceOrTarget)) {
                            continue;
                        }
                        CcCiClassInfo linkClass = rltClassHashMap.get(constructRltId);
                        if(org.apache.commons.lang3.StringUtils.isNotBlank(linkCiStr)&& org.apache.commons.lang3.StringUtils.isBlank(oldLinkCiStr)){
                            //新增
                            String s = stdMap.get(releAssetAttrDef.getProStdName());
                            if(org.apache.commons.lang3.StringUtils.isNotBlank(s)) {
                                JSONArray objects = com.alibaba.fastjson.JSON.parseArray(s);
                                for (int i = 0; i < objects.size(); i++) {
                                    String linkCiCode = objects.getJSONObject(i).getString("ciCode");
                                    ESCIRltInfo esciRltInfo = new ESCIRltInfo();
                                    esciRltInfo.setClassId(linkClass.getCiClass().getId());
                                    if ("target".equalsIgnoreCase(sourceOrTarget)) {
                                        String key = ciInfo.getCi().getCiCode() + "_" + linkClass.getCiClass().getId() + "_" + linkCiCode;
                                        esciRltInfo.setCiCode(key);
                                        esciRltInfo.setUniqueCode("UK_" + key);
                                        esciRltInfo.setSourceCiCode(ciInfo.getCi().getCiCode());
                                        esciRltInfo.setSourceClassId(ciInfo.getCi().getClassId());
                                        esciRltInfo.setTargetCiCode(linkCiCode);
                                        esciRltInfo.setTargetClassId(Long.parseLong(releAssetAttrDef.getProDropSourceDef()));
                                    } else {
                                        String key =  linkCiCode+ "_" + linkClass.getCiClass().getId() + "_" + ciInfo.getCi().getCiCode();
                                        esciRltInfo.setCiCode(key);
                                        esciRltInfo.setUniqueCode("UK_" + key);
                                        esciRltInfo.setSourceCiCode(linkCiCode);
                                        esciRltInfo.setSourceClassId(Long.parseLong(releAssetAttrDef.getProDropSourceDef()));
                                        esciRltInfo.setTargetCiCode(ciInfo.getCi().getCiCode());
                                        esciRltInfo.setTargetClassId(ciInfo.getCi().getClassId());
                                    }
                                    rltInfoList.add(esciRltInfo);
                                }
                            }
                        }else if(org.apache.commons.lang3.StringUtils.isNotBlank(oldLinkCiStr)&& org.apache.commons.lang3.StringUtils.isBlank(linkCiStr)){
                            //全部删除
                            JSONArray objects = com.alibaba.fastjson.JSON.parseArray(oldLinkCiStr);
                            for (int i = 0; i < objects.size(); i++) {
                                String linkCiCode = objects.getJSONObject(i).getString("ciCode");
                                if ("target".equalsIgnoreCase(sourceOrTarget)) {
                                    String key = ciInfo.getCi().getCiCode() + "_" + linkClass.getCiClass().getId() + "_" + linkCiCode;
                                    delRltCiCodeSet.add(key);
                                } else {
                                    String key =  linkCiCode+ "_" + linkClass.getCiClass().getId() + "_" + ciInfo.getCi().getCiCode();
                                    delRltCiCodeSet.add(key);
                                }
                            }
                        }else if(org.apache.commons.lang3.StringUtils.isNotBlank(oldLinkCiStr)&& org.apache.commons.lang3.StringUtils.isNotBlank(linkCiStr)){
                            //判断
                            JSONArray oldAttrJsonArr = com.alibaba.fastjson.JSON.parseArray(oldLinkCiStr);
                            JSONArray newAttrJsonArr = com.alibaba.fastjson.JSON.parseArray(linkCiStr);
                            Set<String> oldCiCodes = new HashSet<>();
                            for (int i=0;i< oldAttrJsonArr.size();i++){
                                oldCiCodes.add(oldAttrJsonArr.getJSONObject(i).getString("ciCode"));
                            }
                            Set<String> newCiCodes = new HashSet<>();
                            for (int i=0;i< newAttrJsonArr.size();i++){
                                newCiCodes.add(newAttrJsonArr.getJSONObject(i).getString("ciCode"));
                            }

                            Sets.SetView<String> needAddCodes = Sets.difference(newCiCodes, oldCiCodes);
                            Sets.SetView<String> needDelCodes = Sets.difference(oldCiCodes,newCiCodes);
                            //新增
                            for (String needAddCode : needAddCodes) {
                                ESCIRltInfo esciRltInfo = new ESCIRltInfo();
                                esciRltInfo.setClassId(linkClass.getCiClass().getId());
                                if ("target".equalsIgnoreCase(sourceOrTarget)) {
                                    String key = ciInfo.getCi().getCiCode() + "_" + linkClass.getCiClass().getId() + "_" + ciCode;
                                    esciRltInfo.setCiCode(key);
                                    esciRltInfo.setUniqueCode("UK_" + key);
                                    esciRltInfo.setSourceCiCode(ciInfo.getCi().getCiCode());
                                    esciRltInfo.setSourceClassId(ciInfo.getCi().getClassId());
                                    esciRltInfo.setTargetCiCode(needAddCode);
                                    esciRltInfo.setTargetClassId(Long.parseLong(releAssetAttrDef.getProDropSourceDef()));
                                } else {
                                    String key =  needAddCode+ "_" + linkClass.getCiClass().getId() + "_" + ciInfo.getCi().getCiCode();
                                    esciRltInfo.setCiCode(key);
                                    esciRltInfo.setUniqueCode("UK_" + key);
                                    esciRltInfo.setSourceCiCode(needAddCode);
                                    esciRltInfo.setSourceClassId(Long.parseLong(releAssetAttrDef.getProDropSourceDef()));
                                    esciRltInfo.setTargetCiCode(ciInfo.getCi().getCiCode());
                                    esciRltInfo.setTargetClassId(ciInfo.getCi().getClassId());
                                }
                                rltInfoList.add(esciRltInfo);
                            }
                            //删除
                            for (String needDelCode : needDelCodes) {
                                if ("target".equalsIgnoreCase(sourceOrTarget)) {
                                    String key = ciInfo.getCi().getCiCode() + "_" + linkClass.getCiClass().getId() + "_" + needDelCode;
                                    delRltCiCodeSet.add(key);
                                } else {
                                    String key =  needDelCode+ "_" + linkClass.getCiClass().getId() + "_" + ciInfo.getCi().getCiCode();
                                    delRltCiCodeSet.add(key);
                                }
                            }
                        }
                    }
                }else {
                    //新增直接建立新关系
                    for (CcCiAttrDef releAssetAttrDef : releAssetAttrDefs) {
                        String s = stdMap.get(releAssetAttrDef.getProStdName());
                        Long constructRltId = releAssetAttrDef.getConstructRltId();
                        String sourceOrTarget = releAssetAttrDef.getSourceOrTarget();
                        if (constructRltId == null || org.apache.commons.lang3.StringUtils.isBlank(sourceOrTarget)) {
                            continue;
                        }
                        CcCiClassInfo linkClass =  rltClassHashMap.get(constructRltId);
                        if(org.apache.commons.lang3.StringUtils.isNotBlank(s)){
                            JSONArray objects = com.alibaba.fastjson.JSON.parseArray(s);
                            for (int i =0;i<objects.size();i++){
                                String linkCiCode = objects.getJSONObject(i).getString("ciCode");
                                ESCIRltInfo esciRltInfo = new ESCIRltInfo();
                                esciRltInfo.setClassId(linkClass.getCiClass().getId());
                                if ("target".equalsIgnoreCase(sourceOrTarget)) {
                                    String key = ciInfo.getCi().getCiCode() + "_" + linkClass.getCiClass().getId() + "_" + linkCiCode;
                                    esciRltInfo.setCiCode(key);
                                    esciRltInfo.setUniqueCode("UK_" + key);
                                    esciRltInfo.setSourceCiCode(ciInfo.getCi().getCiCode());
                                    esciRltInfo.setSourceClassId(ciInfo.getCi().getClassId());
                                    esciRltInfo.setTargetCiCode(linkCiCode);
                                    esciRltInfo.setTargetClassId(Long.parseLong(releAssetAttrDef.getProDropSourceDef()));
                                } else {
                                    String key =  linkCiCode+ "_" + linkClass.getCiClass().getId() + "_" + ciInfo.getCi().getCiCode();
                                    esciRltInfo.setCiCode(key);
                                    esciRltInfo.setUniqueCode("UK_" + key);
                                    esciRltInfo.setSourceCiCode(linkCiCode);
                                    esciRltInfo.setSourceClassId(Long.parseLong(releAssetAttrDef.getProDropSourceDef()));
                                    esciRltInfo.setTargetCiCode(ciInfo.getCi().getCiCode());
                                    esciRltInfo.setTargetClassId(ciInfo.getCi().getClassId());
                                }
                                rltInfoList.add(esciRltInfo);
                            }
                        }
                    }
                }
            }
            List<String> repeatKeys = new ArrayList<>();
            // 校验hashCode是否重复
            Map<String, Integer> res = this.checkRecordsByHashCode(ciInfosList, repeatKeys, updateLogMap);
            ignores += res.get("ignore") == null ? 0 : res.get("ignore");
            updates += res.get("update") == null ? 0 : res.get("update");
            if (!BinaryUtils.isEmpty(repeatKeys)) {
                for (String str : repeatKeys) {
                    rowMessages.add(this.buildRowMessage(keyToIndex.get(str), CheckAttrUtil.EXIST, "业务主键【" + str + "】重复"));
                }
            }
            // 保存CI操作日志
            List<ESCIOperateLog> ciLogs = new ArrayList<ESCIOperateLog>(updateLogMap.values());
            for (ESCIOperateLog ciLog : ciLogs) {
                ciLog.setSourceId(sourceId);
                ciLog.setCiClassName(ciClassInfo.getClassName());
                ciLog.setProNames(attrDefs.stream().map(CcCiAttrDef::getProName).collect(Collectors.toList()));
                ciLog.setOperator(loginUser == null ? "system" : loginUser.getLoginCode());
            }
            Set<String> keySet = updateLogMap.keySet();
            if (ciInfosList.size() > keySet.size()) {
                ciInfosList.forEach(ciInfo -> {
                    if (BinaryUtils.isEmpty(ciInfo.getCi().getCiCode())) {
                        ciInfo.getCi().setCiCode(String.valueOf(ciInfo.getCi().getId()));
                    }
                    String ciCode = ciInfo.getCi().getCiCode();
                    if (!keySet.contains(ciCode)) {
                        ciLogs.add(buildLogRecord(sourceId, SysUtil.StaticUtil.LOG_DYNAMIC_INSERT, attrDefs, null, ciInfo.getAttrs(), ciClassInfo.getClassName(), ciInfo.getCi()));
                    }
                });
            }
            Map<String, Object> saveOrUpdateMsg = esCiSvc.saveOrUpdateCIBatch(ciInfosList, false);
            // if (!BinaryUtils.isEmpty(saveOrUpdateMsg.get("failCount"))) {
            // failCount += (Integer) saveOrUpdateMsg.get("failCount");
            // }
            inserts = (totals - failCount - updates - ignores);
            successCount = inserts + updates;
            if (!BinaryUtils.isEmpty(saveOrUpdateMsg.get("errMessge"))) {
                String errStr = JSON.toString(saveOrUpdateMsg.get("errMessge"));
                // 入库失败
                rowMessages.add(this.buildRowMessage(1, CheckAttrUtil.FAILURE, "入库失败：" + errStr));
            }
            //保存关系
            if (!CollectionUtils.isEmpty(rltInfoList)) {
                ciRltSvc.bindBatchCiRlt(rltInfoList, SysUtil.getCurrentUserInfo().getLoginCode());
            }
            if(!CollectionUtils.isEmpty(delRltCiCodeSet)){
                ciRltSvc.delRltByIdsOrRltCodes(null,delRltCiCodeSet,null);
            }
            this.saveCIOperateLogBatch(ciLogs);
            // saveMessage.add(saveOrUpdateMsg);
        }
        // 汇总整理结果
        detailedResult.setSuccessNum(successCount);
        detailedResult.setFailNum(failCount);
        detailedResult.setInsertNum(inserts);
        detailedResult.setUpdateNum(updates);
        detailedResult.setIgnoreNum(ignores);
        detailedResult.setTotalNum(totals);
        detailedResult.setRowMessages(rowMessages);
        return detailedResult;
    }

    private Map<Long, Map<String, ESCIInfo>> getClassCiGroup(List<Long> releClassIdList) {
        Map<Long, Map<String, ESCIInfo>> classIdGroupCiMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(releClassIdList)) {
            List<ESCIInfo> releCIList = new ArrayList<>();
            ESCISearchBean bean = new ESCISearchBean();
            bean.setClassIds(releClassIdList);
            bean.setPageSize(10000);
            Page<ESCIInfo> esciInfoPage = esCiSvc.searchESCIByBean(bean);
            releCIList.addAll(esciInfoPage.getData());
            for (int i = 2; i < esciInfoPage.getTotalPages() + 1; i++) {
                bean.setPageNum(i);
                Page<ESCIInfo> ciByBean = esCiSvc.searchESCIByBean(bean);
                releCIList.addAll(ciByBean.getData());
                if (CollectionUtils.isEmpty(ciByBean.getData())) {
                    break;
                }
            }
            for (ESCIInfo esciInfo : releCIList) {
                Map<String, ESCIInfo> classPrimarykeyMap = classIdGroupCiMap.computeIfAbsent(esciInfo.getClassId(), k -> new HashMap<String, ESCIInfo>());
                String ciPrimaryKey = esciInfo.getCiPrimaryKey();
                List<String> ciPrimaryList = JSONObject.parseArray(ciPrimaryKey, String.class);
                ciPrimaryList.remove(0);
                classPrimarykeyMap.put(ciPrimaryList.stream().collect(Collectors.joining("|")), esciInfo);
            }
        }
        return classIdGroupCiMap;
    }

    @SuppressWarnings("unchecked")
    private Map<String, List<String>> getExterDictValues(Long domainId, List<CcCiAttrDef> attrDefs) {
        Assert.isTrue(!BinaryUtils.isEmpty(attrDefs), "X_PARAM_NOT_NULL${name:attrDefs}");
        Map<String, List<String>> dictValMap = new HashMap<>();
        List<CcCiAttrDef> dictDefs =
                attrDefs.stream().filter(def -> def.getProType().intValue() == AttrNameKeyEnum.DICT.getType() && !BinaryUtils.isEmpty(def.getProDropSourceDef())).collect(Collectors.toList());
        for (CcCiAttrDef def : dictDefs) {
            try {
                Long dictClassId = CiClassProDropSourceDefHelper.getCiClassProDropSourceClassId(def.getProDropSourceDef().trim());
                Long[] dictDefIds = CiClassProDropSourceDefHelper.getCiClassProDropSourceDefIds(def.getProDropSourceDef().trim());
                List<String> dictValues = dictSvc.getExteralDictValues(domainId, dictClassId, dictDefIds);
                dictValMap.put(def.getProName().toUpperCase(), dictValues);
            } catch (Exception e) {
                Assert.isTrue(false, "属性[" + def.getProName().toUpperCase() + "]引用不合法");
            }
        }
        return dictValMap;
    }

    /**
     * icon path conversion
     *
     * @param attrDefs attributes def
     * @param attrs    ci attributes
     */
    private void removeModelIconPath(List<CcCiAttrDef> attrDefs, Map<String, String> attrs) {
        if (isShow3dAttribute) {
            String getProStdName = this.getModelProStdName(attrDefs);
            if (!"".equals(getProStdName)) {
                this.convertModelPath(attrs, getProStdName, false);
            }
        }
    }

    /**
     * Get the attribute name corresponding to the 3D model
     *
     * @param bean        ESCISearchBean
     * @param ciGroupPage ciGroupPage
     */
    private void addModelIconPath(ESCISearchBean bean, CiGroupPage ciGroupPage) {
        if (isShow3dAttribute) {
            long classId = bean.getCdt().getClassId();
            ESCIClassInfo esciClassInfo = esClsSvc.getById(classId);
            String modelAttrDef = this.getModelProStdName(esciClassInfo.getCcAttrDefs());

            List<CcCiInfo> ccCiInfos = ciGroupPage.getData();
            if (!"".equals(modelAttrDef)) {
                ccCiInfos.forEach(ciInfo -> {
                    Map<String, String> dbCiAttrs = ciInfo.getAttrs();
                    if (dbCiAttrs != null && !dbCiAttrs.isEmpty()) {
                        this.convertModelPath(dbCiAttrs, modelAttrDef, true);
                    }
                });
            }
        }
    }


    /**
     * Get the attribute name corresponding to the 3D model
     *
     * @param attrDefs CcCiAttrDef
     */
    private String getModelProStdName(List<CcCiAttrDef> attrDefs) {
        String modelAttrDef = "";
        List<String> modelAttrDefNames = new ArrayList<>(1);

        attrDefs.forEach(attr -> {
            int attrType = attr.getProType();
            if (attrType == AttrNameKeyEnum.MODEL.getType()) {
                modelAttrDefNames.add(attr.getProStdName());
            }
        });
        if (modelAttrDefNames.size() > 0) {
            modelAttrDef = modelAttrDefNames.get(0);
        }
        return modelAttrDef;
    }


    /**
     * Get the attribute name corresponding to the 3D model
     *
     * @param attrs      attributes definition
     * @param proStdName 3D model attribute name
     * @param isAddPath  add url path / remove url path
     */
    private void convertModelPath(Map<String, String> attrs, String proStdName, Boolean isAddPath) {
        String modelVal = attrs.get(proStdName);
        if (modelVal != null) {
            if (modelVal.startsWith(this.urlPath)) {
                modelVal = modelVal.replaceAll(this.urlPath, "");
            }
            if (isAddPath) {
                modelVal = this.urlPath + modelVal;
            }
            attrs.put(proStdName, modelVal);
        }
    }


    /**
     * When one-click import, check the path of the image containing the 3D model
     *
     * @param _3DModelAttrName 3D type attribute name
     * @param attrs            With check attribute collection
     * @param imagesPath       Existing image path
     */
    private void _3DmodelAttributeImgPathVerification(String _3DModelAttrName, Map<String, String> attrs, Set<String> imagesPath) {
        String iconPath = attrs.get(_3DModelAttrName);
        if (iconPath != null) {
            if (!imagesPath.contains(iconPath)) {
                String defaultPath = esClsSvc.getDefault3dModel();
                attrs.put(_3DModelAttrName, defaultPath);
            }
        }
    }


    /**
     * When exporting CI, check and remove the path
     *
     * @param attrDefs attrDefs
     * @param attrs    With check attribute collection
     */
    private void ciExport3DmodelAttrPathCheck(List<CcCiAttrDef> attrDefs, Map<String, Object> attrs) {
        if (isShow3dAttribute) {
            String _3DModelAttributeDefinition = this.getModelProStdName(attrDefs);
            if (null != _3DModelAttributeDefinition && !"".equals(_3DModelAttributeDefinition)) {
                Object _3DAttrValue = attrs.get(_3DModelAttributeDefinition);
                if (null != _3DAttrValue) {
                    String _3DAttrValueStr = _3DAttrValue.toString();
                    if (_3DAttrValueStr.startsWith(this.urlPath)) {
                        _3DAttrValueStr = _3DAttrValueStr.replaceAll(this.urlPath, "");
                    }
                    attrs.put(_3DModelAttributeDefinition, _3DAttrValueStr);
                }
            }
        }
    }

    /**
     * KEY转换成大写
     *
     * @param map
     * @return
     */
    public Map<String, String> toStdMap(Map<String, String> map) {
        Map<String, String> ret = new HashMap<String, String>();
        if (map == null) {
            return ret;
        }
        Set<String> keySet = map.keySet();
        for (String key : keySet) {
            if (key != null) {
                ret.put(key.toUpperCase(), map.get(key));
            } else {
                ret.put(null, map.get(key));
            }
        }
        return ret;
    }

    private ImportRowMessage buildRowMessage(Integer rowNum, Integer errorType, String message) {
        ImportRowMessage rowMessage = new ImportRowMessage();
        rowMessage.setRowNum(rowNum);
        ImportCellMessage cellMessage = new ImportCellMessage();
        cellMessage.setErrorType(errorType);
        cellMessage.setErrorDesc(message);
        rowMessage.setMessageItems(Collections.singletonList(cellMessage));
        return rowMessage;
    }

    private boolean compareCiPrimaryKeys(List<String> ciPks1, List<String> ciPks2) {
        try {
            return CommUtil.compareToCiPks(ciPks1, ciPks2);
        } catch (Exception e) {
            log.error("do compareToCiPks 【" + JSON.toString(ciPks1) + "】 and 【 " + JSON.toString(ciPks2) + "】 err!");
        }
        return false;
    }

    protected Map<String, Integer> checkRecordsByHashCode(List<CcCiInfo> ciInfosList, List<String> repeatKeys, Map<String, ESCIOperateLog> updateLogMap) {
        // updateLogMap用于判断根据ciCode更新的CI，避免更新操作重复记录
        Map<String, Integer> res = new HashMap<>();
        int ignore = 0;
        int update = 0;
        Set<Integer> hashCodes = new HashSet<>();
        ciInfosList.forEach(ciInfo -> hashCodes.add(ciInfo.getCi().getHashCode()));
        long liveCisCount = esCiSvc.countByCondition(QueryBuilders.termsQuery("hashCode", hashCodes));
        if (liveCisCount <= 0) {
            return res;
        }
        List<CcCiInfo> liveCis = esCiSvc.getCIInfoPageByQuery(1, new BigDecimal(liveCisCount).intValue(), QueryBuilders.termsQuery("hashCode", hashCodes), false).getData();
        Map<Integer, List<CcCiInfo>> dbCIs = new HashMap<>();
        liveCis.forEach(ciInfo -> {
            CcCi ci = ciInfo.getCi();
            if (dbCIs.get(ci.getHashCode()) == null) {
                dbCIs.put(ci.getHashCode(), new LinkedList<CcCiInfo>());
            }
            dbCIs.get(ci.getHashCode()).add(ciInfo);
        });
        Iterator<CcCiInfo> iterator = ciInfosList.iterator();
        while (iterator.hasNext()) {
            CcCiInfo ciInfo = iterator.next();
            // 校验是否与库中的CI重复
            if (dbCIs.containsKey(ciInfo.getCi().getHashCode())) {
                List<CcCiInfo> esInfos = dbCIs.get(ciInfo.getCi().getHashCode());
                for (CcCiInfo esInfo : esInfos) {
                    CcCi ci = esInfo.getCi();
                    if (compareCiPrimaryKeys(JSON.toList(ciInfo.getCi().getCiPrimaryKey(), String.class), JSON.toList(ci.getCiPrimaryKey(), String.class))) {
                        // 同分类下业务主键重复，更新
                        boolean isUpdate = ci.getClassId().longValue() == ciInfo.getCi().getClassId().longValue()
                                && (BinaryUtils.isEmpty(ciInfo.getCi().getCiCode()) || ci.getCiCode().equals(ciInfo.getCi().getCiCode()));
                        if (isUpdate) {
                            if (!updateLogMap.containsKey(ci.getCiCode())) {
                                update++;
                                ESCIOperateLog ciLog = ESCIOperateLog.builder().ciId(ci.getId()).ciCode(ci.getCiCode()).ciPrimaryKey(ciInfo.getCi().getCiPrimaryKey())
                                        .dynamic(SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE).newAttrs(ciInfo.getAttrs()).oldAttrs(esInfo.getAttrs()).libaryId(1).build();
                                updateLogMap.put(ci.getCiCode(), ciLog);
                            }
                            ciInfo.getCi().setCiVersion(esInfo.getCi().getCiVersion());
                            // 属性合并
                            Map<String, String> combineAttrs = new HashMap<>();
                            combineAttrs.putAll(esInfo.getAttrs());
                            combineAttrs.putAll(ciInfo.getAttrs());
                            ciInfo.setAttrs(combineAttrs);
                            ci.setLocalVersion(0L);
                            ci.setPublicVersion(esInfo.getCi().getPublicVersion());
                            if (!CheckAttrUtil.checkAttrMapEqual(combineAttrs, esInfo.getAttrs())) {
                                ciInfo.getCi().setCiVersion(String.valueOf(Long.parseLong(esInfo.getCi().getCiVersion()) + 1L));
                                ci.setPublicVersion(esInfo.getCi().getPublicVersion() + 1L);
                            }
                            ciInfo.getCi().setId(ci.getId());
                            ciInfo.getCi().setCiCode(ci.getCiCode());
                            ciInfo.getCi().setCreateTime(ci.getCreateTime());
                            ciInfo.getCi().setCreator(ci.getCreator());
                        } else {
                            if (updateLogMap.containsKey(ciInfo.getCi().getCiCode())) {
                                update--;
                                updateLogMap.remove(ciInfo.getCi().getCiCode());
                            }
                            ignore++;
                            repeatKeys.add(ciInfo.getCi().getCiPrimaryKey());
                            iterator.remove();
                            break;
                        }
                    }
                }
            }
            if (BinaryUtils.isEmpty(ciInfo.getCi().getCiCode())) {
                ciInfo.getCi().setCiCode(String.valueOf(ciInfo.getCi().getId()));
            }
        }
        res.put("ignore", ignore);
        res.put("update", update);
        return res;
    }

    @Override
    public Map<Long, Long> countCiNumGroupClsByQuery(ESCISearchBean bean) {
        Map<Long, Long> clsIdCiNumMap = new HashMap<>();
        bean = bean == null ? new ESCISearchBean() : bean;
        QueryBuilder query = commSvc.getCIQueryBuilderByBean(bean);
        Map<String, Long> countRes = esCiSvc.groupByCountField("classId", query);
        countRes.forEach((clsId, ciNum) -> clsIdCiNumMap.put(Long.valueOf(clsId), ciNum));
        return clsIdCiNumMap;
    }

    private Long saveCIOperateLog(Long sourceId, Integer dynamic, List<CcCiAttrDef> attrDefs, Map<String, String> oldAttrs, Map<String, String> newAttrs, String className, CcCi ci) {
        ESCIOperateLog log = buildLogRecord(sourceId, dynamic, attrDefs, oldAttrs, newAttrs, className, ci);
        return logSvc.saveOrUpdate(log);
    }

    private Integer saveCIOperateLogBatch(List<ESCIOperateLog> logs) {
        return logSvc.saveOrUpdateBatch(logs);
    }

    /**
     * 构建CI操作日志实例
     *
     * @param sourceId  来源ID 1：页面；2：配置处理；3：DIX
     * @param dynamic   动态 1：添加；2：删除；3修改
     * @param oldAttrs  旧属性值
     * @param newAttrs  新属性值
     * @param className 分类名， 可不传
     * @param ci        CiId必填
     * @return
     */
    public static ESCIOperateLog buildLogRecord(Long sourceId, Integer dynamic, List<CcCiAttrDef> attrDefs,
                                                Map<String, String> oldAttrs, Map<String, String> newAttrs, String className, CcCi ci) {
        Assert.notNull(dynamic, "X_PARAM_NOT_NULL${name:dynamic}");
        Assert.notNull(className, "X_PARAM_NOT_NULL${name:className}");
        Assert.notNull(ci, "X_PARAM_NOT_NULL${name:ci}");
        Assert.notNull(ci.getCiCode(), "X_PARAM_NOT_NULL${name:ciCode}");
        Assert.notNull(ci.getCiPrimaryKey(), "X_PARAM_NOT_NULL${name:ciPrimaryKey}");
        Assert.notEmpty(attrDefs, "X_PARAM_NOT_NULL${name:attrDefs}");
        // getRealAttrs(newAttrs, attrDefs);
        // getRealAttrs(oldAttrs, attrDefs);
        ESCIOperateLog record = new ESCIOperateLog();
        if (dynamic.equals(SysUtil.StaticUtil.LOG_DYNAMIC_INSERT)) {
            Assert.notEmpty(newAttrs, "X_PARAM_NOT_NULL${name:newAttrs}");
            record.setNewAttrs(newAttrs);
        } else if (dynamic.equals(SysUtil.StaticUtil.LOG_DYNAMIC_DELETE)) {
            Assert.notEmpty(oldAttrs, "X_PARAM_NOT_NULL${name:oldAttrs}");
            record.setOldAttrs(oldAttrs);
        } else if (dynamic.equals(SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE)) {
            Assert.notEmpty(newAttrs, "X_PARAM_NOT_NULL${name:newAttrs}");
            Assert.notEmpty(oldAttrs, "X_PARAM_NOT_NULL${name:oldAttrs}");
            record.setNewAttrs(newAttrs);
            record.setOldAttrs(oldAttrs);
        }
        if (sourceId == null) {
            sourceId = 1L;
        }
        record.setSourceId(sourceId);
        if (sourceId == 1L) {
            try {
                record.setOperator(SysUtil.getCurrentUserInfo().getLoginCode());
            } catch (Exception e) {
                record.setOperator("system");
            }
        } else if (sourceId == 2L) {
            record.setOperator("CP");
        } else if (sourceId == 3L) {
            record.setOperator("DIX");
        } else {
            record.setOperator("system");
        }
        record.setCiId(ci.getId());
        record.setCiCode(ci.getCiCode());
        record.setCiPrimaryKey(ci.getCiPrimaryKey());
        record.setCiClassName(className);
        record.setDynamic(dynamic);
        record.setLibaryId(1);
        record.setProNames(attrDefs.stream().map(CcCiAttrDef::getProName).collect(Collectors.toList()));
        return record;
    }

    @Override
    public Long countByQuery(ESCISearchBean bean) {
        bean = bean == null ? new ESCISearchBean() : bean;
        QueryBuilder query = commSvc.getCIQueryBuilderByBean(bean);
        return esCiSvc.countByCondition(query);
    }

    // /**
    // * 校验属性是否变化
    // *
    // * @param newAttrs
    // * @param oldAttrs
    // * @return
    // */
    // private boolean checkAttrChange(Map<String, String> newAttrs, Map<String,
    // String> oldAttrs) {
    // Map<String, String> checkNewAttrs = newAttrs.entrySet().stream()
    // .filter((e) -> !BinaryUtils.isEmpty(e.getValue()))
    // .collect(Collectors.toMap((e) -> e.getKey(), (e) -> e.getValue()));
    // Map<String, String> checkOldAttrs = oldAttrs.entrySet().stream()
    // .filter((e) -> !BinaryUtils.isEmpty(e.getValue()))
    // .collect(Collectors.toMap((e) -> e.getKey(), (e) -> e.getValue()));
    // Iterator<Map.Entry<String, String>> it =
    // checkOldAttrs.entrySet().iterator();
    // while (it.hasNext()) {
    // String key = it.next().getKey();
    // boolean isDuplicate = (checkOldAttrs.get(key) == null &&
    // checkNewAttrs.get(key) == null)
    // || (checkOldAttrs.get(key) != null && checkNewAttrs.get(key) != null
    // && checkOldAttrs.get(key).equals(checkNewAttrs.get(key)));
    // if (isDuplicate) {
    // it.remove();
    // checkNewAttrs.remove(key);
    // }
    // }
    // return checkNewAttrs.size() > 0 || checkOldAttrs.size() > 0 ? true :
    // false;
    // }
    @Override
    public Page<String> getAttrValuesBySearchBean(ESAttrAggBean searchBean) {
        Long classId = searchBean.getClassId();
        String field = searchBean.getAttrName();
        Assert.notNull(classId, "X_PARAM_NOT_NULL${name:classId}");
        Assert.notNull(field, "X_PARAM_NOT_NULL${name:attrName}");
        Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
        return esCiSvc.queryAttrVal(domainId, searchBean);
    }

    @Override
    public Page<ESCIInfo> getESCIInfoPageByQuery(Long domainId, int pageNum, int pageSize, QueryBuilder query, List<SortBuilder<?>> sorts, Boolean isHighLight) {
        if (isHighLight == null) {
            isHighLight = false;
        }
        if (isHighLight) {
            return esCiSvc.getSortListByHighLightQuery(pageNum, pageSize, query, sorts, null);
        } else {
            return esCiSvc.getSortListByQuery(pageNum, pageSize, query, sorts);
        }
    }

   /* @Override
    public Integer removeAllCI(Long domainId, QueryBuilder query) {
        return null;
    }

    @Override
    public boolean modifyAttrValueBatch(CIAttrValueUpdateDto dto) {
        return false;
    }*/

   /* @Override
    public Integer removeCiBatch(CIRemoveBatchDto dto) {
        dto.valid();
        Long classId = dto.getClassId();
        List<Long> ciIds = dto.getCiIds();
        boolean flag = dto.isAll();
        List<Long> unCiIds = dto.getUnCiIds();
        if (flag) {
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("classId", classId));
            if (!BinaryUtils.isEmpty(unCiIds)) {
                query.mustNot(QueryBuilders.termsQuery("id", unCiIds));
            }

            return esCiSvc.deleteByQuery(query, true);
        }
        //否则,批量删除已选的数据
        return this.removeByIds(ciIds, 1L);
    }*/

    @Override
    public Integer removeAllCI(Long domainId, QueryBuilder query) {
        return null;
    }

    @Override
    public boolean modifyAttrValueBatch(CIAttrValueUpdateDto dto) {
        return false;
    }

    @Override
    public Integer removeCiBatch(CIRemoveBatchDto dto) {
        return null;
    }

    @Override
    public boolean updateAttrValueBatch(CIAttrValueUpdateDto dto) {
        dto.valid();
        String proName = dto.getProName();
        String value = dto.getValue();
        // 获取属性信息
        ESCIClassInfo classInfo = esClsSvc.getById(dto.getClassId());
        Optional<ESCIAttrDefInfo> findFirst = classInfo.getAttrDefs().stream()
                .filter(def -> def.getProName().equals(proName)).findFirst();
        Assert.isTrue(findFirst.isPresent(), "属性[" + proName + "]不存在");
        ESCIAttrDefInfo def = findFirst.get();
        Assert.isTrue(def.getIsMajor().intValue() == 0, "不可批量修改主键值");
        Assert.isTrue(def.getIsRequired().intValue() == 0 || !BinaryUtils.isEmpty(value), "必填属性值不可为空");
        String attrKey = def.getProStdName();
        // 校验属性值格式
        Integer checkResult = CheckAttrUtil.validateAttrValType(def, value);
        Assert.isTrue(CheckAttrUtil.SUCCESS == checkResult, "属性值错误");
        // 获取关联的CI，直接批量修改属性值，CI操作日志和历史版本不好处理，取出来再存
        List<ESCIInfo> esciInfos = esCiSvc
                .getListByQuery(QueryBuilders.boolQuery().must(QueryBuilders.termQuery("classId", dto.getClassId()))
                        .must(QueryBuilders.termsQuery("id", dto.getCiIds())));
        List<ESCIOperateLog> logs = new ArrayList<>();
        // 构建CI操作日志
        for (ESCIInfo esciInfo : esciInfos) {
            CcCiInfo ciInfo = commSvc.tranCcCiInfo(esciInfo, false);
            Map<String, String> attrs = ciInfo.getAttrs();
            Map<String, String> newAttrs = new HashMap<>(attrs);
            if (attrs.containsKey(attrKey)) {
                newAttrs.put(attrKey, value);
                ESCIOperateLog log = buildLogRecord(null, SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE,
                        classInfo.getCcAttrDefs(), attrs, newAttrs, classInfo.getClassName(), ciInfo.getCi());
                logs.add(log);
                esciInfo.getAttrs().put(attrKey, value);
            }
            esciInfo.setLocalVersion(0L);
            esciInfo.setPublicVersion(esciInfo.getPublicVersion());
            if (!CheckAttrUtil.checkAttrMapEqual(newAttrs, attrs)) {
                esciInfo.setVersion(esciInfo.getVersion() + 1L);
                esciInfo.setPublicVersion(esciInfo.getPublicVersion() + 1L);
            }
        }
        this.saveCIOperateLogBatch(logs);
        Integer res = esCiSvc.saveOrUpdateBatch(esciInfos);
        return res != 0;
    }

    @Override
    public Map<String, Long> queryCiCountByClassId() {
        return esCiSvc.groupByCountField("classId", QueryBuilders.existsQuery("id"));
    }

    @Override
    public Page<ESCIInfo> queryCiInfoPage(int pageNum, int pageSize, QueryBuilder query, String sortField, boolean isAsc) {
        return esCiSvc.getSortListByQuery(pageNum, pageSize, query, sortField, isAsc);
    }


    @Override
    public Integer removeByOwnerCodeAndClassId(Long classId, String ownerCode) {
        long sourceId = 1L;
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("classId", classId));
        long count = esCiSvc.countByCondition(query);
        if (count > 0) {
            long totalPages = count % 3000 == 0 ? count / 3000 : count / 3000 + 1;
            int pageNum = 0;
            while (pageNum < totalPages) {
                List<ESCIInfo> esciInfos = esCiSvc.getListByQuery(++pageNum, 3000, query).getData();
                List<CcCiInfo> ciInfos = commSvc.transEsInfoList(esciInfos, true);
                // List<CcCiInfo> ciInfos =
                // esCiSvc.getCIInfoPageByQuery(pageNum++, 3000, query,
                // true).getData();
                List<ESCIOperateLog> ciLogs = new ArrayList<>();
                ciInfos.forEach(ciInfo -> {
                    ciLogs.add(buildLogRecord(sourceId, SysUtil.StaticUtil.LOG_DYNAMIC_DELETE, ciInfo.getAttrDefs(), ciInfo.getAttrs(), null, ciInfo.getCiClass().getClassName(), ciInfo.getCi()));
                });
                this.saveCIOperateLogBatch(ciLogs);
                esCiSvc.transCIAttrs(esciInfos, false);
                esHistorySvc.saveOrUpdateHistoryInfosBatch(esciInfos, ESCIHistoryInfo.ActionType.DELETE);
            }
            ciRltSvc.delRltByCiClassId(classId);
            esCiSvc.removeByClassId(classId);
        }
        return 1;
    }

    @Override
    public Map<String, SaveBatchCIContext> saveOrUpdateBatchCI(List<ESCIInfo> ciList, List<Long> classIds, String ownerCode, String loginCode) {
        Assert.notNull(ciList, "X_PARAM_NOT_NULL${name:ciList}");
        Assert.notNull(classIds, "X_PARAM_NOT_NULL${name:classIds}");
        Assert.notNull(ownerCode, "X_PARAM_NOT_NULL${name:ownerCode}");
        Assert.notNull(loginCode, "X_PARAM_NOT_NULL${name:loginCode}");
        Map<Long, ESCIClassInfo> classMap = esClsSvc.selectMapByQuery(1, classIds.size(), QueryBuilders.termsQuery("id", classIds));
        Map<String, SaveBatchCIContext> paramsContext = new HashMap<>();
        for (ESCIInfo item : ciList) {
            ESCIInfo ci = item;
            Assert.notNull(ci, "X_PARAM_NOT_NULL${name:ci}");
            Long classId = item.getClassId();
            Assert.notNull(classId, "X_PARAM_NOT_NULL${name:classId}");
            String ciCode = item.getCiCode();
            Assert.notNull(ciCode, "X_PARAM_NOT_NULL${name:ciCode}");
            SaveBatchCIContext context = paramsContext.computeIfAbsent(item.getCiCode(), k -> new SaveBatchCIContext(ciCode, classId, item));
            Map<String, Object> attrs = item.getAttrs();
            context.setId(ci.getId());
            context.setAttrsObj(attrs);
            context.setAttrsStr(coverToAttrs(attrs));
            boolean isAdd = BinaryUtils.isEmpty(context.getId()) ? true : false;
            String ciOwnerCode = BinaryUtils.isEmpty(ci.getOwnerCode()) ? ownerCode : ci.getOwnerCode();
            context.setOwnerCode(ciOwnerCode);
            context.setAdd(isAdd);
            ESCIClassInfo ciClass = classMap.get(classId);
            if (ciClass == null) {
                throw MessageException.i18n("BS_MNAME_CLASS_NOT_EXSIT");
            }
            context.setClassName(ciClass.getClassName());
            context.setClassStdCode(ciClass.getClassStdCode());
            // 校验CI属性-当前及父类属性
            List<CcCiAttrDef> attrDefs = classMap.get(classId).getCcAttrDefs();
            // When the 3D model is opened, save the CI and add the path conversion
            removeModelIconPath(attrDefs, context.getAttrsStr());
            Map<String, Integer> checkResult = commSvc.validAttrs(attrDefs, context.getAttrsStr(), true);
            if (!BinaryUtils.isEmpty(checkResult)) {
                for (String result : checkResult.keySet()) {
                    throw new MessageException(result);
                }
            }
            context.setAttrsObj(attrs);
            context.setAttrsStr(coverToAttrs(attrs));
            Map<String, String> stdMap = CheckAttrUtil.toStdMap(context.getAttrsStr());
            context.setStdMap(stdMap);
            context.setAttrDefs(attrDefs);
            // 校验数据字典类型-暂不支持
            /*Map<String, List<String>> dictValuesMap = this.getExterDictValues(attrDefs);
            if (!BinaryUtils.isEmpty(dictValuesMap)) {
                Iterator<Entry<String, List<String>>> it = dictValuesMap.entrySet().iterator();
                while (it.hasNext()) {
                    Entry<String, List<String>> next = it.next();
                    String key = next.getKey();
                    List<String> values = next.getValue();

                    String val = stdMap.get(key);
                    Assert.isTrue(!isAdd || BinaryUtils.isEmpty(val) || values.contains(val), "属性[" + key + "]引用值[" + val + "]不存在");
                }
            }*/

            // 校验重复
            List<String> ciPKAttrDefNames = CommUtil.getCiPKAttrDefNames(attrDefs);
            Integer hashCode = CommUtil.getCiMajorHashCode(context.getAttrsStr(), ciPKAttrDefNames, context.getClassStdCode());
            List<String> ciPrimaryKeys = CommUtil.getCiPrimaryKeys(context.getClassStdCode(), context.getAttrsStr(), ciPKAttrDefNames);
            context.setPrimaryKeys(ciPrimaryKeys);
            context.setHashCode(hashCode);
        }
        Map<Integer, List<String>> hashCodes = new HashMap<>(paramsContext.size());
        Set<Long> rltIds = new HashSet<>();
        for (SaveBatchCIContext ctx : paramsContext.values()) {
            boolean contains = hashCodes.containsKey(ctx.getHashCode());
            List<String> ciCodes = hashCodes.computeIfAbsent(ctx.getHashCode(), k -> Lists.newArrayList(ctx.getCiCode()));
            if (contains) {
                for (String ciCode : ciCodes) {
                    SaveBatchCIContext repeatCtx = paramsContext.get(ciCode);
                    if (compareCiPrimaryKeys(repeatCtx.getPrimaryKeys(), ctx.getPrimaryKeys())) {
                        throw MessageException.i18n("BS_MNAME_RECORD_CONTAINS");
                    }
                }
                ciCodes.add(ctx.getCiCode());
            }
            ctx.getAttrDefs().forEach(attrDef -> {
                if (attrDef.getProType() == ESPropertyType.LINK_CI.getValue() && attrDef.getConstructRltId() != null) {
                    rltIds.add(attrDef.getConstructRltId());
                }
            });
        }
        Map<Long, CcCiClassInfo> rltClassMap = new HashMap<>();
        if (!BinaryUtils.isEmpty(rltIds)) {
            CCcCiClass cdt = new CCcCiClass();
            cdt.setIds(rltIds.toArray(new Long[0]));
            List<CcCiClassInfo> rltClasses = iRltClassSvc.getRltClassByCdt(cdt);
            rltClassMap = rltClasses.stream().collect(Collectors.toMap(ccCiClassInfo -> ccCiClassInfo.getCiClass().getId(), ccCiClassInfo -> ccCiClassInfo, (k1, k2) -> k1));
        }

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termsQuery("hashCode", hashCodes.keySet()));
        boolQuery.must(QueryBuilders.termsQuery("classId", new HashSet<>(classIds)));
        boolQuery.must(QueryBuilders.termQuery("domainId", 1L));
        List<CcCiInfo> liveCis = esCiSvc.getCIInfoPageByQuery(1, 3000, boolQuery, false).getData();
        for (CcCiInfo cInfo : liveCis) {
            List<String> ciCodes = hashCodes.get(cInfo.getCi().getHashCode());
            if (BinaryUtils.isEmpty(ciCodes)) {
                continue;
            }
            for (String ciCode : ciCodes) {
                SaveBatchCIContext repeatCtx = paramsContext.get(ciCode);
                if (compareCiPrimaryKeys(repeatCtx.getPrimaryKeys(), JSON.toList(cInfo.getCi().getCiPrimaryKey(), String.class))) {
                    boolean idEqual = repeatCtx.getId() != null && repeatCtx.getId().longValue() == cInfo.getCi().getId().longValue();
                    boolean classIdEqual = repeatCtx.getClassId().longValue() == cInfo.getCi().getClassId().longValue();
                    boolean isUpdate = idEqual || classIdEqual;
                    if (isUpdate) {
                        if (repeatCtx.getId() != null) {
                            log.error("CI已存在!:{}",JSON.toString(repeatCtx));
                            if(!idEqual){
                                throw new BinaryException("[设计库]数据已存在,主键冲突-:" + repeatCtx.getPrimaryKeys());
                            }
                            //Assert.isTrue(idEqual, "BS_CI_NO_EXIST");
                        }
                        repeatCtx.setId(cInfo.getCi().getId());
                        repeatCtx.setAdd(false);
                    } else {
                        throw new BinaryException("[设计库]数据已存在,主键冲突:" + repeatCtx.getPrimaryKeys());
                        //throw MessageException.i18n("BS_MNAME_RECORD_CONTAINS");
                    }
                }
            }
        }
        List<SaveBatchCIContext> addContext = paramsContext.values().stream().filter(ctx -> ctx.isAdd()).collect(Collectors.toList());
        if (!BinaryUtils.isEmpty(addContext)) {
            List<String> ciCodes = addContext.stream().map(SaveBatchCIContext::getCiCode).collect(Collectors.toList());
            List<ESCIInfo> ciCodeQuery = esCiSvc.getListByQuery(QueryBuilders.termsQuery("ciCode.keyword", ciCodes));
            if (!BinaryUtils.isEmpty(ciCodeQuery)) {
                for (ESCIInfo dbCi : ciCodeQuery) {
                    SaveBatchCIContext saveBatchCIContext = paramsContext.get(dbCi.getCiCode());
                    saveBatchCIContext.setId(dbCi.getId());
                    saveBatchCIContext.setAdd(false);
                }
            }
        }
        List<SaveBatchCIContext> updateContext = paramsContext.values().stream().filter(ctx -> !ctx.isAdd()).collect(Collectors.toList());
        Map<Long, ESCIInfo> ciDbMap = new HashMap<>();
        if (!BinaryUtils.isEmpty(updateContext)) {
            List<Long> ciIds = updateContext.stream().map(SaveBatchCIContext::getId).collect(Collectors.toList());
            List<ESCIInfo> ciCodeQuery = esCiSvc.getListByQuery(QueryBuilders.termsQuery("id", ciIds));
            if (!BinaryUtils.isEmpty(ciCodeQuery)) {
                for (ESCIInfo esciInfo : ciCodeQuery) {
                    ciDbMap.put(esciInfo.getId(), esciInfo);
                }
            }
        }
        HashMap<String, SaveBatchCIContext> stringSaveDesignBatchCIContextHashMap = new HashMap<>();
        for (SaveBatchCIContext ctx : paramsContext.values()) {
            // 属性过滤，只保存定义过的属性
            Iterator<String> itAttr = ctx.getStdMap().keySet().iterator();
            List<String> defNames = ctx.getAttrDefs().stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList());
            while (itAttr.hasNext()) {
                String attrName = itAttr.next();
                // 属性不包含在定义的属性，并且不是下划线开头结尾的字段，去掉
                if (!defNames.contains(attrName) && !attrName.matches("_([\\d\\D]*?)_")) {
                    itAttr.remove();
                }
            }
            Map<String, Object> oldAttrsObj = null;
            // 组装CI
            if (ctx.isAdd()) {
                long uuid = ESUtil.getUUID();
                ctx.getEsCi().setId(uuid);
                ctx.getEsCi().setCiCode(ctx.getCiCode());
                ctx.getEsCi().setCreator(loginCode);
                ctx.getEsCi().setCreateTime(ESUtil.getNumberDateTime());
                ctx.getEsCi().setCiVersion(String.valueOf(1));
                ctx.getEsCi().setLocalVersion(0L);
                ctx.getEsCi().setPublicVersion(1);
            } else {
                ESCIInfo dbCIInfo = ciDbMap.get(ctx.getId());
                Assert.notNull(dbCIInfo, "BS_CI_NO_EXIST");
                oldAttrsObj = dbCIInfo.getAttrs();
                ctx.getEsCi().setId(dbCIInfo.getId());
                ctx.getEsCi().setCiCode(dbCIInfo.getCiCode());
                ctx.getEsCi().setCreateTime(dbCIInfo.getCreateTime());
                ctx.getEsCi().setLocalVersion(0L);
                ctx.getEsCi().setCiVersion(dbCIInfo.getCiVersion());
                ctx.getEsCi().setPublicVersion(dbCIInfo.getPublicVersion());
                ctx.setOldAttrs(coverToAttrs(oldAttrsObj));
                if (!CheckAttrUtil.checkAttrMapEqual(ctx.getStdMap(), ctx.getOldAttrs())) {
                    ctx.getEsCi().setCiVersion(String.valueOf(Long.parseLong(dbCIInfo.getCiVersion()) + 1L));
                    ctx.getEsCi().setPublicVersion(dbCIInfo.getPublicVersion() + 1L);
                    ctx.setChange(true);
                }
                stringSaveDesignBatchCIContextHashMap.put(ctx.getCiCode(),ctx);
            }
            ctx.getEsCi().setClassId(ctx.getClassId());
            ctx.getEsCi().setOwnerCode(ctx.getOwnerCode());
            ctx.getEsCi().setDataStatus(1);
            ctx.getEsCi().setHashCode(ctx.getHashCode());
            ctx.getEsCi().setCiPrimaryKey(JSON.toString(ctx.getPrimaryKeys()));
        }
        List<ESCIInfo> esCiList = paramsContext.values().stream().map(SaveBatchCIContext::getEsCi).collect(Collectors.toList());
        List<ESCIInfo> esciInfos = esCiSvc.saveOrUpdateCiList(esCiList);
        esCiSvc.transCIAttrs(esciInfos, true);
        List<CcCiInfo> ccCiInfos = commSvc.transEsInfoList(esciInfos, true);

        List<ESCIRltInfo> allRlts = new ArrayList<>();
        for (CcCiInfo ccCiInfo : ccCiInfos) {
            SaveBatchCIContext saveBatchCIContext = stringSaveDesignBatchCIContextHashMap.get(ccCiInfo.getCi().getCiCode());
            if(saveBatchCIContext == null){
                continue;
            }
            List<ESCIRltInfo> esciRltInfos = parseLinkCIRlts(ccCiInfo, saveBatchCIContext.getOldAttrs(),rltClassMap);
            if(!CollectionUtils.isEmpty(esciRltInfos)){
                allRlts.addAll(esciRltInfos);
            }
        }
        if (!CollectionUtils.isEmpty(allRlts)) {
            ciRltSvc.bindBatchCiRlt(allRlts, ownerCode);
        }
        for (SaveBatchCIContext ctx : paramsContext.values()) {
            if (ctx.isAdd()) {
                this.saveCIOperateLog(null, SysUtil.StaticUtil.LOG_DYNAMIC_INSERT, ctx.getAttrDefs(), null, ctx.getStdMap(), ctx.getClassName(), ctx.getEsCi());
            } else {
                this.saveCIOperateLog(null, SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE, ctx.getAttrDefs(), ctx.getOldAttrs(), ctx.getStdMap(), ctx.getClassName(), ctx.getEsCi());
            }
        }
        return paramsContext;
    }

    @Override
    public Map<String, SaveBatchCIContext> copyCiListByIds(List<ESCIInfo> ciList, String ownerCode) {
        return null;
    }

    private Map<String, String> coverToAttrs(Map<String, Object> attrs) {
        Map<String, String> newAttrs = new HashMap<>();
        if (BinaryUtils.isEmpty(attrs)) {
            return newAttrs;
        }
        attrs.forEach((k, v) -> {
            if (!BinaryUtils.isEmpty(v)) {
                newAttrs.put(k, String.valueOf(v));
            }
        });
        return newAttrs;
    }

    public Map<String, Long> getCICodeMaxVersion(List<String> ciCodes) {
        return esHistorySvc.getCICodeMaxVersion(ciCodes);
    }

    public Map<String, Long> getMaxVersionByPrimaryKey(List<String> primaryKeys) {
        return esHistorySvc.getMaxVersionByPrimaryKey(primaryKeys);
    }

    public CiGroupPage queryPageBySearchBeanVO(@CIViewPermission ESCISearchBeanVO bean, boolean hasClass) {
        CiGroupPage ciGroupPage = this.getCiGroupList(bean, hasClass);
        // The current 3D scene, open the 3D model path to add
        this.addModelIconPath(bean, ciGroupPage);
        Set<String> set = new HashSet<>();
        ciGroupPage.getData().forEach(info -> {
            set.add(info.getCi().getOwnerCode());
        });
        Map<String, String> userMap = new HashMap<>();
        if (!BinaryUtils.isEmpty(set)) {
            CSysUser user = new CSysUser();
            String[] codes = set.toArray(new String[set.size()]);
            user.setLoginCodes(codes);
            List<SysUser> sysUserByCdt = userSvc.getSysUserByCdt(user);
            userMap = sysUserByCdt.stream().collect(Collectors.toMap(SysUser::getLoginCode, SysUser::getUserName));
        }
        for (CcCiInfo info : ciGroupPage.getData()) {
            Map<String, String> attrs = info.getAttrs();
            String userName = "admin";
            String currentUser = userMap.get(info.getCi().getOwnerCode());
            if (!BinaryUtils.isEmpty(currentUser)) {
                userName = currentUser;
            }
            attrs.put("所属用户", userName);
        }
        return ciGroupPage;
    }

    /**
     * 资产可见->筛选数据
     * @param bean
     * @return
     */
    private CiGroupPage getCiGroupList(ESCISearchBeanVO bean, boolean hasClass) {
        if (bean.getArtifactId() != null) {
            Map<Long, String> filterMap = null;
            List<EamArtifactElementVo> elementList = artifactColumnSvc.queryByArtifactId(bean.getArtifactId(), Lists.newArrayList(ArtifactType.ASSET_TYPE.val()));
            if (!BinaryUtils.isEmpty(elementList)) {
                filterMap = new HashMap<>();
                for (EamArtifactElementVo artifactElementVo : elementList) {
                    List<String> elements = artifactElementVo.getElements();
                    for (String element : elements) {
                        EamArtifactCiVo vo = com.alibaba.fastjson.JSON.parseObject(element, EamArtifactCiVo.class);
                        if (BinaryUtils.isEmpty(vo.getType()) || !vo.getType().equals("class") || !vo.getAssetsFlag()) {
                            continue;
                        }
                        Long classId = bean.getCdt().getClassId();
                        if (!StringUtils.isEmpty(vo.getFilter()) && Objects.equals(classId, Long.valueOf(vo.getId()))) {
                            filterMap.put(Long.valueOf(vo.getId()), vo.getFilter());
                        }
                    }
                }
            }
            if (filterMap != null && filterMap.keySet().size() > 0) {
                int size = bean.getPageSize();
                for (Map.Entry<Long, String> filterInfo : filterMap.entrySet()) {
                    List<Long> classList = new ArrayList<>(1);
                    classList.add(filterInfo.getKey());
                    bean.setClassIds(classList);
                    // 获取过滤条件
                    String filterStr = filterInfo.getValue();
                    if (!StringUtils.isEmpty(filterStr)) {
                        JSONArray jsonArray = JSONArray.parseArray(filterStr);
                        if (jsonArray.size() > 0) {
                            List<CcCiInfo> allClassCiList = new ArrayList<>();
                            for (int i = 0; i < jsonArray.size(); i++) {
                                JSONArray jsonArr = jsonArray.getJSONArray(i);
                                List<ESAttrBean> andAttrs = new ArrayList<>();
                                for (int j = 0; j < jsonArr.size(); j++) {
                                    JSONObject filterJson = jsonArr.getJSONObject(j);
                                    if (filterJson != null && !StringUtils.isEmpty(filterJson.getString("name"))
                                            && !StringUtils.isEmpty(filterJson.getString("value"))) {
                                        ESAttrBean esAttrBean = new ESAttrBean();
                                        esAttrBean.setKey(filterJson.getString("name"));
                                        esAttrBean.setOptType(1);
                                        esAttrBean.setValue(filterJson.getString("value"));
                                        andAttrs.add(esAttrBean);
                                    }
                                }
                                bean.setAndAttrs(andAttrs);
                                bean.setPageSize(3000);
                                CiGroupPage page = esCiSvc.queryPageBySearchBeanVO(bean, hasClass);
                                if (page != null && !CollectionUtils.isEmpty(page.getData())) {
                                    if (page.getTotalRows() > 3000) {
                                        bean.setPageSize((int)page.getTotalRows());
                                        page = esCiSvc.queryPageBySearchBeanVO(bean, hasClass);
                                    }
                                    List<CcCiInfo> ciList = page.getData();
                                    allClassCiList.addAll(ciList);
                                }
                            }
                            if (!CollectionUtils.isEmpty(allClassCiList)) {
                                // ci去重
                                List<CcCiInfo> collect = new ArrayList<>(allClassCiList.size());
                                Set<String> ciCodeSet = new HashSet<>(allClassCiList.size());
                                for (CcCiInfo ccCiInfo : allClassCiList) {
                                    if (!ciCodeSet.contains(ccCiInfo.getCi().getCiCode())) {
                                        collect.add(ccCiInfo);
                                        ciCodeSet.add(ccCiInfo.getCi().getCiCode());
                                    }
                                }
                                int records = collect.size();
                                int del = records / size;
                                int mod = records % size;
                                Integer total = (mod != 0) ? del + 1 : del;
                                CiGroupPage ciGroupPage = new CiGroupPage(bean.getPageNum(), size, records, total, collect);
                                ciGroupPage.setTotalCiCount(records);
                                return ciGroupPage;
                            }
                        }
                    }
                }
            } else {
                return esCiSvc.queryPageBySearchBeanVO(bean, hasClass);
            }
        }
        return esCiSvc.queryPageBySearchBeanVO(bean, hasClass);
    }

    private Long convertEndTime(String lteTime) {
        String replace = lteTime.replace("/", "")+"240000";
        return Long.valueOf(replace);
    }

    private BoolQueryBuilder buildExportQuery(ExportCiDto exportDto) {
        CCcCi cCcCi = new CCcCi();
        cCcCi.setClassIds(exportDto.getCiClassIds().toArray(new Long[0]));
        ESCISearchBean esciSearchBean = new ESCISearchBean();
        esciSearchBean.setCdt(cCcCi);
        esciSearchBean.setWords(exportDto.getWords());
        return (BoolQueryBuilder)commSvc.getCIQueryBuilderByBean(esciSearchBean);
    }

    private Long convertTime(String convertTime) {
        String replace = convertTime.replace("/", "")+"000000";
        return Long.valueOf(replace);
    }


    /**
     * 资产管理导出，不导出类定义及对象说明
     *
     * @param exportDto
     * @return
     */
    public ResponseEntity<byte[]> exportCiClassAndAttrs(ExportCiVO exportDto) {
        BinaryUtils.checkEmpty(exportDto, "导出条件");
        ResponseEntity<byte[]> res = null;

        if (CollectionUtils.isEmpty(exportDto.getCiClassIds())) {
            throw new BinaryException("分类ID不可为空");
        }
        Set<Long> classIds = exportDto.getCiClassIds();
        // 默认不导出类定义
        Integer hasClsDef = exportDto.getHasClsDef();
        // 默认只导出分类
        Integer hasData = exportDto.getHasData();
        // 默认导出全部类定义
        List<ESCIClassInfo> ciClassInfos = classApiSvc.selectCiClassByIds(Lists.newArrayList(classIds));
        ESCIClassInfo esciClassInfo = ciClassInfos.get(0);
        AppSquareConfigVO appSquare = appSquareConfigSvc.getAppSquareByClassCode(esciClassInfo.getClassCode(), "5");
        String cardName = BinaryUtils.isEmpty(appSquare.getCardName()) ? esciClassInfo.getClassCode() : appSquare.getCardName();
        // 导出类定义
        String fileName = FileUtil.ExcelUtil.getExportFileName(cardName, CommUtil.EXCEL07_XLSX_EXTENSION, true);
        File export = null;
        Workbook swb = null;
        try {
            String path = URLDecoder.decode(ResourceUtils.getURL("classpath:").getPath(), "utf-8");
            File exportDir = new File(path + "/static/download");
            if (!exportDir.exists()) {
                exportDir.mkdirs();
            }
            export = new File(exportDir, "CI-" + CommUtil.EXPORT_TIME_FORMAT.format(new Date()));
            export.mkdir();
            // 填写须知
//            InputStream is = this.getClass().getResourceAsStream("/static_res/ci_new_data_template.xlsx");
//            swb = new SXSSFWorkbook(new XSSFWorkbook(is));
            swb = new SXSSFWorkbook();
        } catch (Exception e) {
            log.error("生成excel失败：", e);
            throw new BinaryException("ci导出报错");
        }
        // 导出类定义
        if (hasClsDef == 1) {
            clsSvc.exportCIClassDef(swb, ciClassInfos);
        }
        // 导出类数据，只导出勾选的分类
        boolean dataNull = true;
        Integer dataCount = 0;
        Integer excelCount = 0;
        if (!(BinaryUtils.isEmpty(classIds) || BinaryUtils.isEmpty(ciClassInfos))) {
            // 标记CI数据是否为空，只要查到数据则设为false
            // 导出数据
            Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
            if (hasData == 1) {
//                fileName = FileUtil.ExcelUtil.getExportFileName(new CIExportLabel().getCIFileName(false), CommUtil.EXCEL07_XLSX_EXTENSION, true);
                for (ESCIClassInfo classInfo : ciClassInfos) {
                    // 校验CI属性-当前及父类属性
                    List<ESCIAttrDefInfo> attrEsDefs = classInfo.getAttrDefs();
                    Map<String, ESCIAttrDefInfo> attrDefInfoMap = attrEsDefs.stream().collect(Collectors.toMap(e -> e.getProName(), e -> e, (k1, k2) -> k1));
                    List<CcCiAttrDef> attrDefs = BeanUtil.converBean(attrEsDefs, CcCiAttrDef.class);
                    if (BinaryUtils.isEmpty(attrDefs)) {
                        continue;
                    }
                    // 定义集合有序保存列头值
                    List<String> titleCellValues = new ArrayList<String>();
//                    Set<String> reqCellValues = new HashSet<String>();
                    // 获取业务主键属性定义
//                    Set<String> pkbCellValues = new HashSet<String>();
                    // 指定ciCode列
                    String majorCellValue = "序号";
                    // 默认第一列为ciCode列
                    titleCellValues.add(majorCellValue);

                    List<String> releAssetAttrNameList = new ArrayList<>();
                    for (CcCiAttrDef attrDef : attrDefs) {
                        // 3D模型属性校验
                        int proType = attrDef.getProType();
                        if (!isShow3dAttribute && proType == AttrNameKeyEnum.MODEL.getType()) {
                            continue;
                        }

                        if (proType == AttrNameKeyEnum.LINK_CI.getType()) {
                            releAssetAttrNameList.add(attrDef.getProName());
                        }
                        // 过滤不展示字段
                        if (!CollectionUtils.isEmpty(exportDto.getAttrsIdMap().get(classInfo.getId()))) {
                            if (!exportDto.getAttrsIdMap().get(classInfo.getId()).contains(attrDef.getId())) {
                                continue;
                            }
                        }
                        titleCellValues.add(attrDef.getProName());

                    }
                    String sheetName = CiExcelUtil.convertSheetNameSpecialChar(classInfo.getClassName());
                    // 创建Sheet并设置标题行
                    Sheet sheet = FileUtil.ExcelUtil.createExcelSheetAndTitle(swb, sheetName, titleCellValues, null, null, null);
                    // if (hasData == 1) {
                    // 兼容指定数据模式，只把指定的id填入到query中当筛选，简单处理沿用游标不做切换(照理说指定数据的就不应该这里再查了因为已经有了)
                    Map<String, Page<ESCIInfo>> rs = null;
                    BoolQueryBuilder exportQuery = QueryBuilders.boolQuery();
                    if (!CollectionUtils.isEmpty(exportDto.getCiIds())) {
                        exportQuery.must(QueryBuilders.termsQuery("id", exportDto.getCiIds()));
                        exportQuery.must(QueryBuilders.termQuery("classId", classInfo.getId()));
                        rs = esCiSvc.getScrollByQuery(1, 2000, exportQuery, "modifyTime", false);
                    }
                    List<ESCIInfo> ciInfos = new ArrayList<>();
                    if (rs != null) {
                        String scrollId = rs.keySet().iterator().next();
                        Page<ESCIInfo> page = rs.get(scrollId);
                        long ciCount = 0;
                        long total = page.getTotalRows();
                        int rowNum = 1;
                        ciInfos.addAll(page.getData());
                        Set<String> releAssetCiCodeList = new HashSet<>();
                        while (ciCount < total) {

                            List<ESCIInfo> secList = esCiSvc.getListByScroll(scrollId);
                            AssetManageUtil.getRlerAssetCiCodeList(releAssetAttrNameList, page, releAssetCiCodeList);
                            ciInfos.addAll(secList);
                            Map<String, String> releAssetMap = new HashMap<>();
                            dataNull = false;
                            // 提取正文值map
                            List<Map<String, String>> commentValues = new ArrayList<Map<String, String>>();
                            int order = 1;
                            for (ESCIInfo info : ciInfos) {
                                Map<String, Object> attrs = info.getAttrs();
                                if (attrs == null || attrs.isEmpty()) {
                                    continue;
                                }
                                attrs.put(majorCellValue, order++);
                                // 导出时, 3D模型属性路径移除
                                this.ciExport3DmodelAttrPathCheck(attrDefs, attrs);
                                // 导出处理关联资产字段
                                AssetManageUtil.ciExportTranRelevancyAsset(attrDefs, attrs, releAssetMap);
//                                AssetManageUtil.ciExportAttrTransition(attrDefs, attrs);
                                Map<String, String> attrStr = com.alibaba.fastjson.JSON.parseObject(com.alibaba.fastjson.JSON.toJSONString(attrs), new TypeReference<Map<String, String>>() {
                                });
                                commentValues.add(attrStr);
                            }
                            // 持续写入正文
                            if (commentValues.size() > 0) {
                                FileUtil.ExcelUtil.writeExcelComment(swb, sheet, rowNum, titleCellValues, null, null, commentValues);
                            }
                            ciCount += ciInfos.size();
                            dataCount += ciInfos.size();
                            rowNum += ciInfos.size();
                            ciInfos.clear();
                            // 数据量达到了单个Excel最大量,且不是最后一页,则需要写完当前Excel再创建新的Excel来存储
                            if (dataCount >= CommUtil.EXCEL_MAX_DATA_COUNT) {
                                excelCount++;
                                try {
                                    String tempFileName = FileUtil.ExcelUtil.getExportFileName(new CIExportLabel().getCIFileName(false), CommUtil.EXCEL07_XLSX_EXTENSION, true);
                                    File output = new File(export, tempFileName);
                                    FileOutputStream fileOutputStream = new FileOutputStream(output);
                                    swb.write(fileOutputStream);
                                    swb.close();
                                    fileOutputStream.close();
                                } catch (Exception e) {
                                    throw BinaryUtils.transException(e, ServiceException.class);
                                }
                                dataCount = 0;
                                rowNum = 1;
                                swb = new SXSSFWorkbook();
                                sheet = FileUtil.ExcelUtil.createExcelSheetAndTitle(swb, sheetName, titleCellValues, null, null, majorCellValue);
                            }
                        }
                        esCiSvc.clearScroll(scrollId);
                    }
                    // }
                }
            }
        }
        File firstFile = null;
        // 末尾数据保存为一个excel
        if (dataCount > 0 || hasData != 1 || dataNull) {
            excelCount++;
            if (excelCount == 1) {
                firstFile = new File(export, fileName);
            }
            try {
                File output = new File(export, fileName);
                FileOutputStream fileOutputStream = new FileOutputStream(output);
                swb.write(fileOutputStream);
                swb.close();
                fileOutputStream.close();
            } catch (Exception e) {
                throw BinaryUtils.transException(e, ServiceException.class);
            }
        }
        if (excelCount > 1) {
            Compression.compressZip(new File(export.getPath()), new File(export.getPath() + ".zip"));
            res = ExcelUtil.returnRes(new File(export.getPath() + ".zip"));
        } else {
            res = ExcelUtil.returnRes(firstFile);
        }
        return res;
    }

//    public ResponseEntity<byte[]> exportCiClassAndAttrs(ExportCiDto exportDto) {
//        BinaryUtils.checkEmpty(exportDto,"导出条件");
//        ResponseEntity<byte[]> res = null;
//
//        if (CollectionUtils.isEmpty(exportDto.getCiClassIds())) {
//            throw new BinaryException("分类ID不可为空");
//        }
//        Set<Long> classIds = exportDto.getCiClassIds();
//        // 默认不导出类定义
//        Integer hasClsDef = exportDto.getHasClsDef();
//        // 默认只导出分类
//        Integer hasData = exportDto.getHasData();
//        // 默认导出全部类定义
//        BoolQueryBuilder query = QueryBuilders.boolQuery();
//        if (!BinaryUtils.isEmpty(classIds)) {
//            query.must(QueryBuilders.termsQuery("id", classIds));
//        }
//        List<ESCIClassInfo> ciClassInfos = esClsSvc.getListByQuery(query);
//        // 导出类定义
//        String fileName = FileUtil.ExcelUtil.getExportFileName(new CIExportLabel().getCIFileName(true), CommUtil.EXCEL07_XLSX_EXTENSION, false);
//        File export = null;
//        Workbook swb = null;
//        try {
//            String path = URLDecoder.decode(ResourceUtils.getURL("classpath:").getPath(), "utf-8");
//            File exportDir = new File(path + "/static/download");
//            if (!exportDir.exists()) {
//                exportDir.mkdirs();
//            }
//            export = new File(exportDir, "CI-" + CommUtil.EXPORT_TIME_FORMAT.format(new Date()));
//            export.mkdir();
//            InputStream is = this.getClass().getResourceAsStream("/static_res/ci_new_data_template.xlsx");
//            swb = new SXSSFWorkbook(new XSSFWorkbook(is));
//        } catch (Exception e) {
//            log.error("生成excel失败：",e);
//            throw new BinaryException("ci导出报错");
//        }
//        // 导出类定义
//        if (hasClsDef == 1) {
//            clsSvc.exportCIClassDef(swb, ciClassInfos);
//        }
//        // 导出类数据，只导出勾选的分类
//        boolean dataNull = true;
//        Integer dataCount = 0;
//        Integer excelCount = 0;
//        if (!(BinaryUtils.isEmpty(classIds) || BinaryUtils.isEmpty(ciClassInfos))) {
//            // 标记CI数据是否为空，只要查到数据则设为false
//            // 导出数据
//            Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
//            if (hasData == 1) {
//                fileName = FileUtil.ExcelUtil.getExportFileName(new CIExportLabel().getCIFileName(false), CommUtil.EXCEL07_XLSX_EXTENSION, true);
//                for (ESCIClassInfo classInfo : ciClassInfos) {
//                    List<CcCiAttrDef> attrDefs = esClsSvc.getAllDefsByClassId(domainId, classInfo.getId());
//                    if (BinaryUtils.isEmpty(attrDefs)) {
//                        continue;
//                    }
//                    // 定义集合有序保存列头值
//                    List<String> titleCellValues = new ArrayList<String>();
//                    Set<String> reqCellValues = new HashSet<String>();
//                    // 获取业务主键属性定义
//                    Set<String> pkbCellValues = new HashSet<String>();
//                    // 指定ciCode列
//                    String majorCellValue = SysUtil.StaticUtil.CICODE_LABEL;
//                    // 默认第一列为ciCode列
//                    titleCellValues.add(majorCellValue);
//                    // 关联资产
//                    List<String> releAssetAttrNameList = new ArrayList<>();
//                    for (CcCiAttrDef attrDef : attrDefs) {
//                        if (attrDef.getIsMajor() == 1) {
//                            pkbCellValues.add(attrDef.getProName());
//                            reqCellValues.add(attrDef.getProName());
//                        } else if (attrDef.getIsRequired() == 1) {
//                            reqCellValues.add(attrDef.getProName());
//                        }
//                        // 3D模型属性校验
//                        int proType = attrDef.getProType();
//                        if (!isShow3dAttribute && proType == AttrNameKeyEnum.MODEL.getType()) {
//                            continue;
//                        }
//                        if (proType == AttrNameKeyEnum.LINK_CI.getType()) {
//                            releAssetAttrNameList.add(attrDef.getProName());
//                        }
//                        // 过滤不展示字段
//                        if (!CollectionUtils.isEmpty(exportDto.getAttrsIdMap().get(classInfo.getId()))) {
//                            if (!exportDto.getAttrsIdMap().get(classInfo.getId()).contains(attrDef.getId())) {
//                                continue;
//                            }
//                        }
//                        titleCellValues.add(attrDef.getProName());
//
//                    }
//                    if (pkbCellValues.isEmpty()) {
//                        continue;
//                    }
//                    String sheetName = CiExcelUtil.convertSheetNameSpecialChar(classInfo.getClassName());
//                    // 创建Sheet并设置标题行
//                    Sheet sheet = FileUtil.ExcelUtil.createExcelSheetAndTitle(swb, sheetName, titleCellValues, reqCellValues, pkbCellValues, majorCellValue);
//                    // if (hasData == 1) {
//                    // 兼容指定数据模式，只把指定的id填入到query中当筛选，简单处理沿用游标不做切换(照理说指定数据的就不应该这里再查了因为已经有了)
//                    Map<String, Page<ESCIInfo>> rs = null;
//                    BoolQueryBuilder exportQuery = QueryBuilders.boolQuery();
//                    if (!CollectionUtils.isEmpty(exportDto.getCiIds())) {
//                        exportQuery.must(QueryBuilders.termsQuery("id", exportDto.getCiIds()));
//                        exportQuery.must(QueryBuilders.termQuery("classId", classInfo.getId()));
//                        rs = esCiSvc.getScrollByQuery(1, 2000, exportQuery, "modifyTime", false);
//                    }
//                    List<ESCIInfo> ciInfos = new ArrayList<>();
//                    if (rs != null) {
//                        String scrollId = rs.keySet().iterator().next();
//                        Page<ESCIInfo> page = rs.get(scrollId);
//                        long ciCount = 0;
//                        long total = page.getTotalRows();
//                        int rowNum = 1;
//                        ciInfos.addAll(page.getData());
//                        Set<String> releAssetCiCodeList = new HashSet<>();
//                        while (ciCount < total) {
//                            List<ESCIInfo> secList = esCiSvc.getListByScroll(scrollId);
//                            AssetManageUtil.getRlerAssetCiCodeList(releAssetAttrNameList, page, releAssetCiCodeList);
//                            ciInfos.addAll(secList);
//                            Map<String, String> releAssetMap = new HashMap<>();
//                            // 根据ciCode 查询关联资产信息
//                            if (!CollectionUtils.isEmpty(releAssetCiCodeList)) {
//                                ESCISearchBean bean = new ESCISearchBean();
//                                bean.setPageSize(releAssetCiCodeList.size());
//                                bean.setCiCodes(Lists.newArrayList(releAssetCiCodeList));
//                                Page<ESCIInfo> esciInfoPage = esCiSvc.searchESCIByBean(bean);
//                                for (ESCIInfo esciInfo : esciInfoPage.getData()) {
//                                    List<String> primaryList = JSONObject.parseArray(esciInfo.getCiPrimaryKey(), String.class);
//                                    primaryList.remove(0);
//                                    String primaryName = primaryList.stream().collect(Collectors.joining("\\|"));
//                                    releAssetMap.put(esciInfo.getCiCode(), primaryName);
//                                }
//                            }
//                            dataNull = false;
//                            // 提取正文值map
//                            List<Map<String, String>> commentValues = new ArrayList<Map<String, String>>();
//                            for (ESCIInfo info : ciInfos) {
//                                Map<String, Object> attrs = info.getAttrs();
//                                if (attrs == null || attrs.isEmpty()) {
//                                    continue;
//                                }
//                                attrs.put(majorCellValue, info.getCiCode());
//
//                                // 导出时, 3D模型属性路径移除
//                                this.ciExport3DmodelAttrPathCheck(attrDefs, attrs);
//                                // 导出处理关联资产字段
//                                AssetManageUtil.ciExportTranRelevancyAsset(attrDefs, attrs, releAssetMap);
//                                Map<String, String> attrStr = com.alibaba.fastjson.JSON.parseObject(com.alibaba.fastjson.JSON.toJSONString(attrs), new TypeReference<Map<String, String>>() {
//                                });
//                                commentValues.add(attrStr);
//                            }
//                            // 持续写入正文
//                            if (commentValues.size() > 0) {
//                                FileUtil.ExcelUtil.writeExcelComment(swb, sheet, rowNum, titleCellValues, null, null, commentValues);
//                            }
//                            ciCount += ciInfos.size();
//                            dataCount += ciInfos.size();
//                            rowNum += ciInfos.size();
//                            ciInfos.clear();
//                            // 数据量达到了单个Excel最大量,且不是最后一页,则需要写完当前Excel再创建新的Excel来存储
//                            if (dataCount >= CommUtil.EXCEL_MAX_DATA_COUNT) {
//                                excelCount++;
//                                try {
//                                    String tempFileName = FileUtil.ExcelUtil.getExportFileName(new CIExportLabel().getCIFileName(false), CommUtil.EXCEL07_XLSX_EXTENSION, true);
//                                    File output = new File(export, tempFileName);
//                                    FileOutputStream fileOutputStream = new FileOutputStream(output);
//                                    swb.write(fileOutputStream);
//                                    swb.close();
//                                    fileOutputStream.close();
//                                } catch (Exception e) {
//                                    throw BinaryUtils.transException(e, ServiceException.class);
//                                }
//                                dataCount = 0;
//                                rowNum = 1;
//                                swb = new SXSSFWorkbook();
//                                sheet = FileUtil.ExcelUtil.createExcelSheetAndTitle(swb, sheetName, titleCellValues, reqCellValues, pkbCellValues, majorCellValue);
//                            }
//                        }
//                        esCiSvc.clearScroll(scrollId);
//                    }
//                    // }
//                }
//            }
//        }
//        File firstFile = null;
//        // 末尾数据保存为一个excel
//        if (dataCount > 0 || hasData != 1 || dataNull) {
//            excelCount++;
//            if (excelCount == 1) {
//                firstFile = new File(export, fileName);
//            }
//            try {
//                File output = new File(export, fileName);
//                FileOutputStream fileOutputStream = new FileOutputStream(output);
//                swb.write(fileOutputStream);
//                swb.close();
//                fileOutputStream.close();
//            } catch (Exception e) {
//                throw BinaryUtils.transException(e, ServiceException.class);
//            }
//        }
//        if (excelCount > 1) {
//            Compression.compressZip(new File(export.getPath()), new File(export.getPath() + ".zip"));
//            res = ExcelUtil.returnRes(new File(export.getPath() + ".zip"));
//        } else {
//            res = ExcelUtil.returnRes(firstFile);
//        }
//        return res;
//    }
}
